/*
|--------------------------------------------------------------------------
| Routes file
|--------------------------------------------------------------------------
|
| The routes file is used for defining the HTTP routes.
|
*/

import router from '@adonisjs/core/services/router'

const DashboardController = () => import('#controllers/dashboard_controller')

// Redirect root to dashboard
router.on('/').redirect('/dashboard')

// Dashboard routes
router.get('/dashboard', [DashboardController, 'index']).as('dashboard')
