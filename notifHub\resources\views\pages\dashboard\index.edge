@layout('layouts/main')

@set('title', 'Dashboard - NotifHub')
@set('breadcrumb', { active: 'Dashboard' })

@section('content')
<!-- [ Main Content ] start -->
<div class="grid grid-cols-12 gap-x-6">
  <!-- Notifications Sent Today -->
  <div class="col-span-12 xl:col-span-4 md:col-span-6">
    <div class="card">
      <div class="card-header !pb-0 !border-b-0">
        <h5>Notifications Sent Today</h5>
      </div>
      <div class="card-body">
        <div class="flex items-center justify-between gap-3 flex-wrap">
          <h3 class="font-light flex items-center mb-0">
            <i class="feather icon-arrow-up text-success-500 text-[30px] mr-1.5"></i>
            1,249
          </h3>
          <p class="mb-0">+12%</p>
        </div>
        <div class="w-full bg-theme-bodybg rounded-lg h-1.5 mt-6 dark:bg-themedark-bodybg">
          <div class="bg-theme-bg-1 h-full rounded-lg shadow-[0_10px_20px_0_rgba(0,0,0,0.3)]" role="progressbar"
            style="width: 75%"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Active Users -->
  <div class="col-span-12 xl:col-span-4 md:col-span-6">
    <div class="card">
      <div class="card-header !pb-0 !border-b-0">
        <h5>Active Users</h5>
      </div>
      <div class="card-body">
        <div class="flex items-center justify-between gap-3 flex-wrap">
          <h3 class="font-light flex items-center mb-0">
            <i class="feather icon-arrow-up text-success-500 text-[30px] mr-1.5"></i>
            8,942
          </h3>
          <p class="mb-0">+8%</p>
        </div>
        <div class="w-full bg-theme-bodybg rounded-lg h-1.5 mt-6 dark:bg-themedark-bodybg">
          <div class="bg-theme-bg-2 h-full rounded-lg shadow-[0_10px_20px_0_rgba(0,0,0,0.3)]" role="progressbar"
            style="width: 85%"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Delivery Rate -->
  <div class="col-span-12 xl:col-span-4">
    <div class="card">
      <div class="card-header !pb-0 !border-b-0">
        <h5>Delivery Rate</h5>
      </div>
      <div class="card-body">
        <div class="flex items-center justify-between gap-3 flex-wrap">
          <h3 class="font-light flex items-center mb-0">
            <i class="feather icon-arrow-up text-success-500 text-[30px] mr-1.5"></i>
            98.5%
          </h3>
          <p class="mb-0">+2%</p>
        </div>
        <div class="w-full bg-theme-bodybg rounded-lg h-1.5 mt-6 dark:bg-themedark-bodybg">
          <div class="bg-theme-bg-1 h-full rounded-lg shadow-[0_10px_20px_0_rgba(0,0,0,0.3)]" role="progressbar"
            style="width: 98%"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="col-span-12 xl:col-span-4">
    <div class="card">
      <div class="card-header">
        <h5>Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 gap-3">
          <a href="#" class="btn btn-primary flex items-center justify-center">
            <i data-feather="plus-circle" class="mr-2"></i>
            Create Notification
          </a>
          <a href="#" class="btn btn-outline-primary flex items-center justify-center">
            <i data-feather="users" class="mr-2"></i>
            Manage Users
          </a>
          <a href="#" class="btn btn-outline-secondary flex items-center justify-center">
            <i data-feather="settings" class="mr-2"></i>
            Templates
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Notification Channels -->
  <div class="col-span-12 xl:col-span-4 md:col-span-6">
    <div class="card">
      <div class="card-header">
        <h5>Notification Channels</h5>
      </div>
      <div class="card-body">
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i data-feather="mail" class="text-primary-500 mr-3"></i>
              <span>Email</span>
            </div>
            <span class="badge bg-success-500 text-white">Active</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i data-feather="smartphone" class="text-primary-500 mr-3"></i>
              <span>SMS</span>
            </div>
            <span class="badge bg-success-500 text-white">Active</span>
          </div>
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i data-feather="bell" class="text-primary-500 mr-3"></i>
              <span>Push</span>
            </div>
            <span class="badge bg-warning-500 text-white">Limited</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Activity -->
  <div class="col-span-12 xl:col-span-4 md:col-span-6">
    <div class="card">
      <div class="card-header">
        <h5>Recent Activity</h5>
      </div>
      <div class="card-body">
        <div class="space-y-3">
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-success-500 rounded-full mt-2"></div>
            <div>
              <p class="mb-1 text-sm font-medium">Welcome email sent</p>
              <p class="text-xs text-muted">2 minutes ago</p>
            </div>
          </div>
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-primary-500 rounded-full mt-2"></div>
            <div>
              <p class="mb-1 text-sm font-medium">New user registered</p>
              <p class="text-xs text-muted">5 minutes ago</p>
            </div>
          </div>
          <div class="flex items-start gap-3">
            <div class="w-2 h-2 bg-warning-500 rounded-full mt-2"></div>
            <div>
              <p class="mb-1 text-sm font-medium">Template updated</p>
              <p class="text-xs text-muted">10 minutes ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Recent Notifications -->
  <div class="col-span-12">
    <div class="card table-card">
      <div class="card-header">
        <h5>Recent Notifications</h5>
      </div>
      <div class="card-body">
        <div class="table-responsive">
          <table class="table table-hover">
            <thead>
              <tr>
                <th>Title</th>
                <th>Recipients</th>
                <th>Channel</th>
                <th>Status</th>
                <th>Sent At</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>
                  <h6 class="mb-1">Welcome to NotifHub</h6>
                  <p class="m-0 text-muted">New user onboarding email</p>
                </td>
                <td>150 users</td>
                <td>
                  <span class="badge bg-primary-100 text-primary-500">
                    <i data-feather="mail" class="w-3 h-3 mr-1"></i>
                    Email
                  </span>
                </td>
                <td>
                  <span class="badge bg-success-500 text-white">Delivered</span>
                </td>
                <td>2 min ago</td>
                <td>
                  <a href="#!" class="btn btn-sm btn-outline-primary">View</a>
                </td>
              </tr>
              <tr>
                <td>
                  <h6 class="mb-1">System Maintenance</h6>
                  <p class="m-0 text-muted">Scheduled maintenance notification</p>
                </td>
                <td>All users</td>
                <td>
                  <span class="badge bg-warning-100 text-warning-500">
                    <i data-feather="bell" class="w-3 h-3 mr-1"></i>
                    Push
                  </span>
                </td>
                <td>
                  <span class="badge bg-success-500 text-white">Delivered</span>
                </td>
                <td>1 hour ago</td>
                <td>
                  <a href="#!" class="btn btn-sm btn-outline-primary">View</a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</div>
<!-- [ Main Content ] end -->
@endsection
