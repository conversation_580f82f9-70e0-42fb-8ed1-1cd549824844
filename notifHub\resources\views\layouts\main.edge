<!doctype html>
<html lang="en">
  <!-- [Head] start -->
  <head>
    <title>{{ title || 'NotifHub - Dashboard' }}</title>
    <!-- [Meta] -->
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="NotifHub - Notification Management Dashboard" />
    <meta name="author" content="NotifHub" />

    <!-- [Favicon] icon -->
    <link rel="icon" href="/assets/images/favicon.svg" type="image/x-icon" />

    <!-- [Font] Family -->
    <link href="https://fonts.googleapis.com/css2?family=Open+Sans:wght@300;400;500;600&display=swap" rel="stylesheet" />
    
    <!-- [phosphor Icons] https://phosphoricons.com/ -->
    <link rel="stylesheet" href="/assets/fonts/phosphor/duotone/style.css" />
    <!-- [Tabler Icons] https://tablericons.com -->
    <link rel="stylesheet" href="/assets/fonts/tabler-icons.min.css" />
    <!-- [Feather Icons] https://feathericons.com -->
    <link rel="stylesheet" href="/assets/fonts/feather.css" />
    <!-- [Font Awesome Icons] https://fontawesome.com/icons -->
    <link rel="stylesheet" href="/assets/fonts/fontawesome.css" />
    <!-- [Material Icons] https://fonts.google.com/icons -->
    <link rel="stylesheet" href="/assets/fonts/material.css" />
    
    <!-- [Template CSS Files] -->
    <link rel="stylesheet" href="/assets/css/style.css" id="main-style-link" />
    
    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @stack('styles')
  </head>
  <!-- [Head] end -->
  
  <!-- [Body] Start -->
  <body data-pc-preset="preset-1" data-pc-sidebar-caption="true" data-pc-layout="vertical" data-pc-direction="ltr" data-pc-theme_contrast="" data-pc-theme="light">
    
    <!-- [ Pre-loader ] start -->
    @include('layouts.partials.loader')
    <!-- [ Pre-loader ] End -->
    
    <!-- [ Sidebar Menu ] start -->
    @include('layouts.partials.sidebar')
    <!-- [ Sidebar Menu ] end -->
    
    <!-- [ Header Topbar ] start -->
    @include('layouts.partials.topbar')
    <!-- [ Header Topbar ] end -->

    <!-- [ Main Content ] start -->
    <div class="pc-container">
      <div class="pc-content">
        
        <!-- [ breadcrumb ] start -->
        @if(breadcrumb)
          @include('layouts.partials.breadcrumb')
        @endif
        <!-- [ breadcrumb ] end -->
        
        <!-- [ Main Content ] start -->
        @!section('content')
        <!-- [ Main Content ] end -->
        
      </div>
    </div>
    <!-- [ Main Content ] end -->

    <!-- [ Footer ] start -->
    @include('layouts.partials.footer')
    <!-- [ Footer ] end -->

    <!-- Required Js -->
    <script src="/assets/js/plugins/popper.min.js"></script>
    <script src="/assets/js/plugins/simplebar.min.js"></script>
    <script src="/assets/js/plugins/feather.min.js"></script>
    <script src="/assets/js/script.js"></script>
    
    @stack('scripts')

    <script>
      // Initialize Feather Icons
      if (typeof feather !== 'undefined') {
        feather.replace();
      }

      layout_change('light');
    </script>
    
    <script>
      layout_sidebar_change('light');
    </script>
    
    <script>
      change_box_container('false');
    </script>
    
    <script>
      layout_caption_change('true');
    </script>
    
    <script>
      layout_rtl_change('false');
    </script>
    
    <script>
      preset_change('preset-1');
    </script>
    
  </body>
  <!-- [Body] end -->
</html>
