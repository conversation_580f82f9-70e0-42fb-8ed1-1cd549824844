/**======================================================================
=========================================================================
Template Name: Datta Able - Tailwind Admin Template
Author: CodedThemes
Support: https://codedthemes.support-hub.io/
File: style.css
=========================================================================
=================================================================================== */
@import url("plugins/simplebar.min.css");
/*! tailwindcss v3.4.10 | MIT License | https://tailwindcss.com */
/*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/
*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}
::before,
::after {
  --tw-content: '';
}
/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/
html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: "Open Sans", sans-serif; /* 4 */
  font-feature-settings: "salt"; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}
/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/
body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}
/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/
hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}
/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/
abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}
/*
Remove the default font size and weight for headings.
*/
h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}
/*
Reset links to optimize for opt-in styling instead of opt-out.
*/
a {
  color: inherit;
  text-decoration: inherit;
}
/*
Add the correct font weight in Edge and Safari.
*/
b,
strong {
  font-weight: bolder;
}
/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/
code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}
/*
Add the correct font size in all browsers.
*/
small {
  font-size: 80%;
}
/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/
sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}
sub {
  bottom: -0.25em;
}
sup {
  top: -0.5em;
}
/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/
table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}
/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/
button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}
/*
Remove the inheritance of text transform in Edge and Firefox.
*/
button,
select {
  text-transform: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/
button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}
/*
Use the modern Firefox focus style for all focusable elements.
*/
:-moz-focusring {
  outline: auto;
}
/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/
:-moz-ui-invalid {
  box-shadow: none;
}
/*
Add the correct vertical alignment in Chrome and Firefox.
*/
progress {
  vertical-align: baseline;
}
/*
Correct the cursor style of increment and decrement buttons in Safari.
*/
::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}
/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/
[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}
/*
Remove the inner padding in Chrome and Safari on macOS.
*/
::-webkit-search-decoration {
  -webkit-appearance: none;
}
/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/
::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}
/*
Add the correct display in Chrome and Safari.
*/
summary {
  display: list-item;
}
/*
Removes the default spacing and border for appropriate elements.
*/
blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}
fieldset {
  margin: 0;
  padding: 0;
}
legend {
  padding: 0;
}
ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}
/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}
/*
Prevent resizing textareas horizontally by default.
*/
textarea {
  resize: vertical;
}
/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/
input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}
/*
Set the default cursor for buttons.
*/
button,
[role="button"] {
  cursor: pointer;
}
/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}
/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/
img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}
/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/
img,
video {
  max-width: 100%;
  height: auto;
}
/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden] {
  display: none;
}
h1,.h1{
  font-size: 48px;
  font-weight: 400;
}
h2,.h2{
  font-size: 44px;
  font-weight: 400;
}
h3,.h3{
  font-size: 26px;
  font-weight: 400;
}
h4,.h4{
  font-size: 20px;
  font-weight: 400;
}
h5,.h5{
  font-size: 18px;
  font-weight: 400;
}
h6,.h6{
  font-size: 14px;
  font-weight: 400;
}
h1, h2, h3, h4, h5, h6, .h1, .h2, .h3, .h4, .h5, .h6{
  line-height: 1.2;
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
h1:is([data-pc-theme="dark"] *), h2:is([data-pc-theme="dark"] *), h3:is([data-pc-theme="dark"] *), h4:is([data-pc-theme="dark"] *), h5:is([data-pc-theme="dark"] *), h6:is([data-pc-theme="dark"] *), .h1:is([data-pc-theme="dark"] *), .h2:is([data-pc-theme="dark"] *), .h3:is([data-pc-theme="dark"] *), .h4:is([data-pc-theme="dark"] *), .h5:is([data-pc-theme="dark"] *), .h6:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
body{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
  font-size: 0.875rem;
  line-height: 1.5;
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
body:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
b, strong{
  font-weight: 400;
}
.text-muted{
  color: rgba(33, 37, 41, 0.75);
}
.text-muted:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.material-icons-two-tone{
  -webkit-background-clip: text;
          background-clip: text;
  -webkit-text-fill-color: transparent;
}
.material-icons-two-tone:not([class*="bg-"]){
  --tw-bg-opacity: 1;
  background-color: rgb(136 136 136 / var(--tw-bg-opacity));
}
.material-icons-two-tone:not([class*="bg-"]):is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
*, ::before, ::after{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
::backdrop{
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}
.container{
  width: 100%;
  margin-right: auto;
  margin-left: auto;
  padding-right: 2rem;
  padding-left: 2rem;
}
@media (min-width: 640px){
  .container{
    max-width: 640px;
  }
}
@media (min-width: 768px){
  .container{
    max-width: 768px;
  }
}
@media (min-width: 1024px){
  .container{
    max-width: 1024px;
  }
}
@media (min-width: 1280px){
  .container{
    max-width: 1280px;
  }
}
@media (min-width: 1536px){
  .container{
    max-width: 1536px;
  }
}
.pc-header{
  position: fixed;
  z-index: 1025;
  display: flex;
  height: 74px;
  background-color: rgba( 244,247,250, 0.7);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
  --tw-backdrop-blur: blur(7px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.pc-header:is([data-pc-theme="dark"] *){
  background-color: rgba( 33, 34, 36, 0.7);
  --tw-text-opacity: 1;
  color: rgb(111 116 127 / var(--tw-text-opacity));
}
.pc-header:where([dir="ltr"], [dir="ltr"] *){
  right: 0px;
}
@media not all and (min-width: 1024px){
  .pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 264px;
  }
}
.pc-header:where([dir="rtl"], [dir="rtl"] *){
  left: 0px;
}
@media not all and (min-width: 1024px){
  .pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
}
@media (min-width: 1024px){
  .pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 264px;
  }
}
.pc-header .pc-head-link{
  position: relative;
  margin-left: 0.25rem;
  margin-right: 0.25rem;
  display: flex;
  height: 2.75rem;
  width: 2.75rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.pc-header .pc-head-link::after{
  position: absolute;
  inset: 0px;
  z-index: 1;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}
.pc-header .pc-head-link:hover::after{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  content: var(--tw-content);
  border-radius: 0.25rem;
}
.pc-header .pc-head-link:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(111 116 127 / var(--tw-text-opacity));
}
.pc-header .pc-head-link:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(40 42 44 / var(--tw-bg-opacity));
}
.pc-header .pc-head-link i, .pc-header .pc-head-link svg, .pc-header .pc-head-link img{
  position: relative;
  z-index: 5;
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.pc-header .pc-head-link i{
  font-size: 1.5rem;
  line-height: 2rem;
  line-height: 1;
}
.pc-header .pc-head-link svg{
  height: 1.25rem;
  width: 1.25rem;
}
.pc-header .pc-head-link:hover i, .pc-header .pc-head-link:hover svg{
  --tw-scale-x: 1.08;
  --tw-scale-y: 1.08;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown{
    position: static;
  }
}
.pc-header .pc-h-item.dropdown .dropdown-menu{
  max-width: 100%;
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown .dropdown-menu{
    left: 15px !important;
    right: 15px !important;
    top: 100% !important;
    min-width: calc(100vw - 30px);
  }
}
.pc-header .pc-h-item.dropdown .dropdown-menu:not(.dropdown-menu-end):where([dir="rtl"], [dir="rtl"] *){
  right: 0px !important;
  left: auto !important;
}
@media not all and (min-width: 640px){
  .pc-header .pc-h-item.dropdown.drp-show .dropdown-menu{
    transform: none !important;
  }
}
@media (min-width: 640px){
  .pc-header .dropdown-menu.drp-search{
    min-width: 320px;
  }
  .pc-header .dropdown-menu.dropdown-notification{
    min-width: 450px;
  }
}
.pc-header .dropdown-menu.dropdown-notification .card{
  cursor: pointer;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.pc-header .dropdown-menu.dropdown-notification .card:hover{
  background-color: rgb(4 169 245 / .05);
}
.pc-header .dropdown-menu.dropdown-user-profile{
  padding: 0px;
}
@media (min-width: 640px){
  .pc-header .dropdown-menu.dropdown-user-profile{
    min-width: 290px;
  }
}
.pc-header .dropdown-menu.dropdown-user-profile .dropdown-item{
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 0.25rem;
}
[data-pc-header*="preset-"] .pc-header .pc-head-link{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-header*="preset-"] .pc-header .pc-head-link::after{
  content: var(--tw-content);
  background-color: rgb(255 255 255 / 0.1);
}
[data-pc-header*="preset-"] .pc-header .pc-head-link:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(111 116 127 / var(--tw-text-opacity));
}
[data-pc-header="preset-1"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
[data-pc-header="preset-2"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
[data-pc-header="preset-3"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
[data-pc-header="preset-4"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
[data-pc-header="preset-5"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
[data-pc-header="preset-6"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}
[data-pc-header="preset-7"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
[data-pc-header="preset-8"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}
[data-pc-header="preset-9"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}
[data-pc-header="preset-10"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
[data-pc-header="preset-11"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}
[data-pc-header="preset-12"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
[data-pc-header="preset-13"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
[data-pc-header="preset-14"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}
[data-pc-header="preset-15"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
[data-pc-header="preset-16"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}
[data-pc-header="preset-17"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}
[data-pc-header="preset-18"] .pc-header{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}
.pc-sidebar{
  position: fixed;
  top: 0px;
  bottom: 0px;
  z-index: 1026;
  width: 264px;
  overflow: hidden;
  --tw-bg-opacity: 1;
  background-color: rgb(63 77 103 / var(--tw-bg-opacity));
  --tw-shadow: 1px 0 20px 0 #3f4d67;
  --tw-shadow-colored: 1px 0 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.pc-sidebar:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
@media not all and (min-width: 1024px){
  .pc-sidebar{
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
  .pc-sidebar:where([dir="ltr"], [dir="ltr"] *){
    left: -264px;
  }
  .pc-sidebar:where([dir="rtl"], [dir="rtl"] *){
    right: -264px;
  }
}
.pc-sidebar .navbar-wrapper{
  width: 264px;
  background-color: inherit;
}
.pc-sidebar .m-header .logo-sm{
  display: none;
}
.pc-sidebar .navbar-content{
  position: relative;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0px;
  padding-right: 0px;
  height: calc(100vh - 74px);
}
@media (min-width: 1024px){
  .pc-sidebar.pc-sidebar-hide{
    width: 0px;
  }
}
.pc-sidebar.pc-sidebar-hide:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 0px;
}
.pc-sidebar.pc-sidebar-hide:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 0px;
}
@media not all and (min-width: 1024px){
  .pc-sidebar.mob-sidebar-active:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
  .pc-sidebar.mob-sidebar-active:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
}
.pc-sidebar.mob-sidebar-active .navbar-wrapper{
  position: relative;
  z-index: 5;
  background-color: inherit;
}
.pc-sidebar .pc-menu-overlay{
  position: fixed;
  inset: 0px;
  background-color: rgba(0,0,0,.15);
  --tw-backdrop-blur: blur(3px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.pc-navbar .pc-caption{
  display: block;
  padding-left: 23px;
  padding-right: 23px;
  padding-top: 1.5rem;
  padding-bottom: 0.5rem;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
.pc-navbar .pc-caption:first-child{
  padding-top: 0.625rem;
}
.pc-navbar .pc-caption:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
.pc-navbar .pc-caption svg{
  display: none;
}
.pc-navbar .pc-link{
  position: relative;
  display: block;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  font-size: 0.875rem;
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
.pc-navbar .pc-link:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
.pc-navbar .pc-link .pc-micon{
  display: inline-block;
  height: 1.5rem;
  width: 1.5rem;
  text-align: center;
  vertical-align: middle;
}
.pc-navbar .pc-link .pc-micon:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 15px;
}
.pc-navbar .pc-link .pc-micon:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 15px;
}
.pc-navbar .pc-link .pc-micon > svg{
  display: inline-block;
  height: 18px;
  width: 18px;
}
.pc-navbar .pc-link .pc-micon > i{
  font-size: 18px;
}
.pc-navbar .pc-link .pc-arrow{
  position: relative;
  display: inline-block;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.pc-navbar .pc-link .pc-arrow::after{
  position: absolute;
  inset: 0px;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar .pc-link .pc-arrow:where([dir="ltr"], [dir="ltr"] *){
  float: right;
}
.pc-navbar .pc-link .pc-arrow:where([dir="rtl"], [dir="rtl"] *){
  float: left;
}
.pc-navbar .pc-link .pc-arrow > svg{
  display: inline-block;
  height: 0.875rem;
  width: 0.875rem;
}
.pc-navbar .pc-link .pc-badge{
  display: inline-flex;
  height: 1.25rem;
  width: 1.25rem;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  font-size: 10px;
  line-height: 1;
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pc-navbar .pc-link .pc-badge:where([dir="ltr"], [dir="ltr"] *){
  float: right;
  margin-right: 5px;
}
.pc-navbar .pc-link .pc-badge:where([dir="rtl"], [dir="rtl"] *){
  float: left;
  margin-left: 5px;
}
.pc-navbar >.pc-item{
  position: relative;
}
.pc-navbar >.pc-item::before{
  position: absolute;
  top: 0px;
  bottom: 0px;
  width: 3px;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar >.pc-item >.pc-link::after{
  position: absolute;
  inset: 0px;
  opacity: 0.1;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar >.pc-item >.pc-link:hover::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.pc-navbar >.pc-item.active >.pc-link{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.pc-navbar >.pc-item.active >.pc-link::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.pc-navbar >.pc-item.pc-trigger, .pc-navbar >.pc-item.active{
  background-color: rgba(0,0,0,0.1);
}
.pc-navbar >.pc-item.pc-trigger::before, .pc-navbar >.pc-item.active::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link{
  font-weight: 500;
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link::after, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  content: var(--tw-content);
  opacity: 1;
}
.pc-navbar >.pc-item .pc-submenu .pc-item.active >.pc-link:hover::after, .pc-navbar >.pc-item .pc-submenu .pc-item.pc-trigger >.pc-link:hover::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.pc-navbar >.pc-item .pc-submenu .pc-link{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-right: 30px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link::after{
  position: absolute;
  top: 1.25rem;
  height: 5px;
  width: 5px;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(169 183 208 / var(--tw-bg-opacity));
  opacity: 0;
  --tw-content: "";
  content: var(--tw-content);
}
.pc-navbar >.pc-item .pc-submenu .pc-link:hover::after{
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  content: var(--tw-content);
  opacity: 1;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 60px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 1.75rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 60px;
}
.pc-navbar >.pc-item .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 1.75rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 5rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 62px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 5rem;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 62px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 95px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 79px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 95px;
}
.pc-navbar >.pc-item .pc-submenu .pc-submenu .pc-submenu .pc-link:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 79px;
}
[data-pc-sidebar-caption="false"] .pc-sidebar .pc-caption{
  display: none;
}
[data-pc-sidebar_theme="true"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 7px 0 15px 0 rgba(69, 90, 100, 0.09);
  --tw-shadow-colored: 7px 0 15px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-pc-sidebar_theme="true"] .pc-sidebar:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(63 77 103 / var(--tw-text-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar .pc-caption:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar .pc-link{
  --tw-text-opacity: 1;
  color: rgb(63 77 103 / var(--tw-text-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar .pc-link:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar >.pc-item.active >.pc-link{
  --tw-text-opacity: 1;
  color: rgb(63 77 103 / var(--tw-text-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar >.pc-item.active >.pc-link::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
[data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar >.pc-item.pc-trigger, [data-pc-sidebar_theme="true"] .pc-sidebar .pc-navbar >.pc-item.active{
  background-color: rgba(0,0,0,0.04);
}
[data-pc-navbar*="preset-"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
[data-pc-navbar*="preset-"] .pc-sidebar .pc-caption:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
[data-pc-navbar*="preset-"] .pc-sidebar .pc-link{
  font-size: 0.875rem;
  color: rgb(255 255 255 / 90);
}
[data-pc-navbar*="preset-"] .pc-sidebar .pc-link:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
[data-pc-navbar="preset-1"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-2"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-3"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-4"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-5"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-6"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-7"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-8"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-9"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-10"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-11"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-12"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-13"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-14"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-15"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-16"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-17"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}
[data-pc-navbar="preset-18"] .pc-sidebar{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-1"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-2"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-3"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-4"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-5"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-6"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-7"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-8"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-9"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-10"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-11"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-12"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-13"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-14"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-15"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-16"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-17"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}
[data-pc-logo="preset-18"] .pc-sidebar .m-header{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}
[data-pc-caption="preset-1"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
[data-pc-caption="preset-2"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
[data-pc-caption="preset-3"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}
[data-pc-caption="preset-4"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}
[data-pc-caption="preset-5"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}
[data-pc-caption="preset-6"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity));
}
[data-pc-caption="preset-7"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
[data-pc-caption="preset-8"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity));
}
[data-pc-caption="preset-9"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity));
}
[data-pc-caption="preset-10"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
}
[data-pc-caption="preset-11"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity));
}
[data-pc-caption="preset-12"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
[data-pc-caption="preset-13"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity));
}
[data-pc-caption="preset-14"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(139 92 246 / var(--tw-text-opacity));
}
[data-pc-caption="preset-15"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity));
}
[data-pc-caption="preset-16"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(217 70 239 / var(--tw-text-opacity));
}
[data-pc-caption="preset-17"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity));
}
[data-pc-caption="preset-18"] .pc-sidebar .pc-caption{
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity));
}
[data-pc-navimg="preset-1"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-1.jpg");
}
[data-pc-navimg="preset-2"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-2.jpg");
}
[data-pc-navimg="preset-3"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-3.jpg");
}
[data-pc-navimg="preset-4"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-4.jpg");
}
[data-pc-navimg="preset-5"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-5.jpg");
}
[data-pc-navimg="preset-6"] .pc-sidebar{
  background-color: rgb(63 77 103 / 0.4);
  background-image: url("../images/layout/navbar-img-6.jpg");
}
[data-pc-drp-menu-icon="preset-1"] .pc-sidebar .pc-arrow > i::before{
  --tw-content: "\ea61";
  content: var(--tw-content);
}
[data-pc-drp-menu-icon="preset-2"] .pc-sidebar .pc-arrow > i::before{
  --tw-content: "\ea65";
  content: var(--tw-content);
}
[data-pc-drp-menu-icon="preset-3"] .pc-sidebar .pc-arrow > i::before{
  --tw-content: "\eb5f";
  content: var(--tw-content);
}
[data-pc-drp-menu-icon="preset-4"] .pc-sidebar .pc-arrow > i::before{
  --tw-content: "\ea69";
  content: var(--tw-content);
}
[data-pc-drp-menu-icon="preset-5"] .pc-sidebar .pc-arrow > i::before{
  --tw-content: "\eb0b";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon*="preset-"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  top: 13px;
  height: auto;
  width: auto;
  transform: none;
  background-color: transparent;
  font-family: "tabler-icons" !important;
  opacity: 0;
}
[data-pc-drp-menu-link-icon*="preset-"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item.active > .pc-link:after, [data-pc-drp-menu-link-icon*="preset-"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item:hover > .pc-link:after{
  opacity: 1;
}
[data-pc-drp-menu-link-icon="preset-1"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon="preset-2"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "\ea1c";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon="preset-3"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "\ea61";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon="preset-4"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "\ea65";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon="preset-5"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "\ea7d";
  content: var(--tw-content);
}
[data-pc-drp-menu-link-icon="preset-6"] .pc-sidebar .pc-navbar > .pc-item:not(.pc-caption) .pc-submenu .pc-item > .pc-link:after{
  --tw-content: "\eaf2";
  content: var(--tw-content);
}
.pc-container{
  position: relative;
  top: 74px;
  min-height: calc(100vh - 135px);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@media not all and (min-width: 1024px){
  .pc-container{
    margin-left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-container:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 264px;
  }
  .pc-container:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 264px;
  }
}
.pc-container .pc-content{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
  padding-top: 1.25rem;
}
@media not all and (min-width: 640px){
  .pc-container .pc-content{
    padding: 15px;
  }
}
.page-header{
  margin-bottom: 1.5rem;
}
@media not all and (min-width: 640px){
  .page-header{
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
}
.page-header .breadcrumb{
  font-size: 13px;
}
.pc-footer{
  position: relative;
  z-index: 995;
  margin-top: 74px;
  padding-top: 15px;
  padding-bottom: 15px;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
@media not all and (min-width: 1024px){
  .pc-footer{
    margin-left: 0px;
  }
}
@media (min-width: 1024px){
  .pc-footer:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 264px;
  }
  .pc-footer:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 264px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-header:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-header:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-container:where([dir="ltr"], [dir="ltr"] *), .pc-sidebar.pc-sidebar-hide ~.pc-footer:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 0px;
  }
  .pc-sidebar.pc-sidebar-hide ~.pc-container:where([dir="rtl"], [dir="rtl"] *), .pc-sidebar.pc-sidebar-hide ~.pc-footer:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 0px;
  }
}
.footer-wrapper.container, .pc-content.container{
  margin-left: auto;
  margin-right: auto;
}
@media (min-width: 768px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 540px;
  }
}
@media (min-width: 1024px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 960px;
  }
}
@media (min-width: 1536px){
  .footer-wrapper.container, .pc-content.container{
    max-width: 1140px;
  }
}
[data-pc-theme_contrast="true"] body{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
[data-pc-theme_contrast="true"] body:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
[data-pc-theme_contrast="true"] .pc-sidebar{
  border-right-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 1px 0 3px 0 rgba(219,224,229,1);
  --tw-shadow-colored: 1px 0 3px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-pc-theme_contrast="true"] .pc-sidebar:is([data-pc-theme="dark"] *){
  border-right-width: 1px;
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
[data-pc-theme_contrast="true"] .card{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
:not(pre) > code[class*=language-], pre[class*=language-]{
  margin: 0px;
  margin-top: 1rem;
  display: flex;
}
:not(pre) > code[class*=language-] > code, pre[class*=language-] > code{
  width: 100%;
}
.apexcharts-legend-text:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
text:is([data-pc-theme="dark"] *),.apexcharts-theme-light .apexcharts-menu-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-reset-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-selection-icon:not(.apexcharts-selected):hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoom-icon:not(.apexcharts-selected):hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoomin-icon:hover svg:is([data-pc-theme="dark"] *), .apexcharts-theme-light .apexcharts-zoomout-icon:hover svg:is([data-pc-theme="dark"] *){
  fill: #fff !important;
}
.apexcharts-gridline:is([data-pc-theme="dark"] *),.apexcharts-xaxis-tick:is([data-pc-theme="dark"] *),.apexcharts-grid-borders:is([data-pc-theme="dark"] *){
  display: none;
}
.apexcharts-canvas{
  direction: ltr;
}
.apexcharts-tooltip.apexcharts-theme-light:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.apexcharts-tooltip.apexcharts-theme-light .apexcharts-tooltip-title:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.apexcharts-xaxistooltip:is([data-pc-theme="dark"] *), .apexcharts-yaxistooltip:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.apexcharts-xaxistooltip-bottom:is([data-pc-theme="dark"] *):after, .apexcharts-xaxistooltip-bottom:is([data-pc-theme="dark"] *):before{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.apexcharts-menu:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.apexcharts-theme-light .apexcharts-menu-item:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.jvm-element:is([data-pc-theme="dark"] *){
  fill: #393b3f !important;
}
.vtree li.vtree-leaf a.vtree-leaf-label:hover:is([data-pc-theme="dark"] *), .vtree li.vtree-leaf.vtree-selected > a.vtree-leaf-label:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity)) !important;
  outline-color: #393b3f !important;
}
.badge{
  display: inline-block;
  border-radius: 0.375rem;
  padding-left: 0.8em;
  padding-right: 0.8em;
  padding-top: 0.45em;
  padding-bottom: 0.45em;
  font-size: .75em;
  font-weight: 500;
  line-height: 1;
}
.btn{
  display: inline-block;
  border-radius: 0.25rem;
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.btn.disabled{
  pointer-events: none;
  cursor: default;
  opacity: 0.75;
}
.btn.btn-lg{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.btn.btn-sm{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.btn.btn-icon{
  display: inline-flex;
  height: 2.5rem;
  width: 2.5rem;
  align-items: center;
  justify-content: center;
  padding: 0px;
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-xl{
  height: 60px;
  width: 60px;
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-l{
  height: 50px;
  width: 50px;
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.btn.btn-icon.avtar-s{
  height: 30px;
  width: 30px;
  font-size: 0.875rem;
}
.btn.btn-icon.avtar-xs{
  height: 1.25rem;
  width: 1.25rem;
  font-size: 0.75rem;
}
.btn-pc-default:not(:hover){
  color: rgb(136 136 136 / 0.7);
}
.btn-pc-default:not(:hover):is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.7);
}
.btn-primary{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.btn-primary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.btn-primary:active{
  --tw-bg-opacity: 1;
  background-color: rgb(2 93 135 / var(--tw-bg-opacity));
}
.btn-secondary{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.btn-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.btn-secondary:active{
  --tw-bg-opacity: 1;
  background-color: rgb(43 50 57 / var(--tw-bg-opacity));
}
.btn-success{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
}
.btn-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
}
.btn-success:active{
  --tw-bg-opacity: 1;
  background-color: rgb(13 137 106 / var(--tw-bg-opacity));
}
.btn-danger{
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
}
.btn-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
}
.btn-danger:active{
  --tw-bg-opacity: 1;
  background-color: rgb(176 20 10 / var(--tw-bg-opacity));
}
.btn-warning{
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
}
.btn-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
}
.btn-warning:active{
  --tw-bg-opacity: 1;
  background-color: rgb(166 127 9 / var(--tw-bg-opacity));
}
.btn-info{
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
}
.btn-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
}
.btn-info:active{
  --tw-bg-opacity: 1;
  background-color: rgb(18 129 166 / var(--tw-bg-opacity));
}
.btn-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
}
.btn-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
}
.btn-dark:active{
  --tw-bg-opacity: 1;
  background-color: rgb(18 20 23 / var(--tw-bg-opacity));
}
.btn-light{
  background-color: rgb(190 198 206 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-light:hover{
  background-color: rgb(190 198 206 / 0.2);
}
.btn-light:focus{
  background-color: rgb(190 198 206 / 0.2);
}
.btn-light:active{
  background-color: rgb(190 198 206 / 0.3);
}
.btn-light:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-link{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.btn-link:hover{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.btn-link:focus{
  background-color: rgb(179 230 254 / 0.2);
}
.btn-link:active{
  background-color: rgb(179 230 254 / 0.3);
}
.btn-outline-primary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  background-color: rgb(4 169 245 / 0);
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.btn-outline-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-primary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-secondary{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
  background-color: rgb(91 107 121 / 0);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-outline-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-success{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(29 233 182 / var(--tw-border-opacity));
  background-color: rgb(29 233 182 / 0);
  --tw-text-opacity: 1;
  color: rgb(29 233 182 / var(--tw-text-opacity));
}
.btn-outline-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-danger{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
  background-color: rgb(244 66 54 / 0);
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.btn-outline-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-warning{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(244 194 43 / var(--tw-border-opacity));
  background-color: rgb(244 194 43 / 0);
  --tw-text-opacity: 1;
  color: rgb(244 194 43 / var(--tw-text-opacity));
}
.btn-outline-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-info{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(62 191 234 / var(--tw-border-opacity));
  background-color: rgb(62 191 234 / 0);
  --tw-text-opacity: 1;
  color: rgb(62 191 234 / var(--tw-text-opacity));
}
.btn-outline-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(33 37 41 / var(--tw-border-opacity));
  background-color: rgb(33 37 41 / 0);
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-outline-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-outline-dark:is([data-pc-theme="dark"] *){
  border-color: rgb(255 255 255 / 0.5);
  color: rgb(255 255 255 / 0.5);
}
.btn-light-primary{
  background-color: rgb(4 169 245 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.btn-light-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-primary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-primary:is([data-pc-theme="dark"] *){
  background-color: rgb(4 169 245 / 0.1);
}
.btn-light-primary:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.btn-light-secondary{
  background-color: rgb(91 107 121 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-light-secondary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-secondary:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-secondary:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-light-secondary:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.btn-light-success{
  background-color: rgb(29 233 182 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(29 233 182 / var(--tw-text-opacity));
}
.btn-light-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-success:is([data-pc-theme="dark"] *){
  background-color: rgb(29 233 182 / 0.1);
}
.btn-light-success:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.btn-light-danger{
  background-color: rgb(244 66 54 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.btn-light-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-danger:is([data-pc-theme="dark"] *){
  background-color: rgb(244 66 54 / 0.1);
}
.btn-light-danger:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
}
.btn-light-warning{
  background-color: rgb(244 194 43 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(244 194 43 / var(--tw-text-opacity));
}
.btn-light-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-warning:is([data-pc-theme="dark"] *){
  background-color: rgb(244 194 43 / 0.1);
}
.btn-light-warning:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
}
.btn-light-info{
  background-color: rgb(62 191 234 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(62 191 234 / var(--tw-text-opacity));
}
.btn-light-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-info:is([data-pc-theme="dark"] *){
  background-color: rgb(62 191 234 / 0.1);
}
.btn-light-info:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
}
.btn-light-dark{
  background-color: rgb(33 37 41 / 0.1);
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-light-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.btn-light-dark:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
  color: rgb(255 255 255 / 0.8);
}
.btn-light-dark:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.btn-link-primary{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.btn-link-primary:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(159 224 253 / var(--tw-bg-opacity));
}
.btn-link-primary:focus{
  background-color: rgb(159 224 253 / 0.5);
}
.btn-link-secondary{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.btn-link-secondary:hover{
  background-color: rgb(178 188 197 / 0.5);
}
.btn-link-secondary:focus{
  background-color: rgb(155 168 180 / 0.5);
}
.btn-link-secondary:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-link-secondary:focus:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.btn-link-success{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(29 233 182 / var(--tw-text-opacity));
}
.btn-link-success:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(178 247 230 / var(--tw-bg-opacity));
}
.btn-link-success:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(141 244 218 / var(--tw-bg-opacity));
}
.btn-link-danger{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.btn-link-danger:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 209 / var(--tw-bg-opacity));
}
.btn-link-danger:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(250 175 170 / var(--tw-bg-opacity));
}
.btn-link-warning{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(244 194 43 / var(--tw-text-opacity));
}
.btn-link-warning:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(252 239 198 / var(--tw-bg-opacity));
}
.btn-link-warning:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(250 227 159 / var(--tw-bg-opacity));
}
.btn-link-info{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(62 191 234 / var(--tw-text-opacity));
}
.btn-link-info:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 240 250 / var(--tw-bg-opacity));
}
.btn-link-info:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(172 228 246 / var(--tw-bg-opacity));
}
.btn-link-dark{
  background-color: transparent;
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.btn-link-dark:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(106 119 131 / var(--tw-bg-opacity));
}
.btn-link-dark:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(88 98 109 / var(--tw-bg-opacity));
}
.btn-link-dark:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.8);
}
.btn-link-dark:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.introjs-tooltipbuttons [role="button"]{
  display: inline-block;
  border-radius: 20px;
  border-width: 1px;
  border-color: transparent;
  background-color: transparent;
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
  padding-left: 1rem;
  padding-right: 1rem;
  text-align: center;
  font-size: 0.875rem;
  font-weight: 500;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: none;
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-prevbutton:active{
  --tw-bg-opacity: 1;
  background-color: rgb(43 50 57 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.introjs-tooltipbuttons [role="button"].introjs-nextbutton:active{
  --tw-bg-opacity: 1;
  background-color: rgb(2 93 135 / var(--tw-bg-opacity));
}
.breadcrumb{
  margin-bottom: 1rem;
  display: flex;
  flex-shrink: 0;
  align-items: center;
  gap: 0.5rem;
}
.breadcrumb .breadcrumb-item:last-child{
  opacity: 0.75;
}
.breadcrumb .breadcrumb-item:not(:last-child){
  position: relative;
}
.breadcrumb .breadcrumb-item:not(:last-child)::before{
  position: absolute;
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1.35rem;
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="ltr"], [dir="ltr"] *)::before{
  right: -20px;
  --tw-content: '\ea61';
  content: var(--tw-content);
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1.35rem;
}
.breadcrumb .breadcrumb-item:not(:last-child):where([dir="rtl"], [dir="rtl"] *)::before{
  left: -20px;
  --tw-content: '\ea60';
  content: var(--tw-content);
}
.breadcrumb .breadcrumb-item:not(:last-child) a{
  transition-duration: 200ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.breadcrumb .breadcrumb-item:not(:last-child) a:hover{
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.card{
  position: relative;
  margin-bottom: 1.5rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 1px 20px 0 rgba(69,90,100,0.08);
  --tw-shadow-colored: 0 1px 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.card:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
  --tw-shadow-color: none;
  --tw-shadow: var(--tw-shadow-colored);
}
.card .card-header{
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  padding: 25px;
}
.card .card-header:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.card .card-header h5{
  font-size: 0.875rem;
  font-weight: 600;
}
.card .card-body{
  padding: 25px;
}
.card .card-footer{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  padding: 25px;
}
.card .card-footer:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.card .card-link{
  margin-right: 0.75rem;
  display: inline-block;
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.card .card-link:hover{
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
.card .card-link:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.btn-group, .dropdown{
  position: relative;
}
.btn-group .dropdown-toggle, .dropdown .dropdown-toggle{
  position: relative;
}
.btn-group .dropdown-toggle::after, .dropdown .dropdown-toggle::after{
  margin-left: 0.25rem;
  vertical-align: bottom;
  font-family: 'tabler-icons';
  font-size: 0.875rem;
  --tw-content: '\ea5f';
  content: var(--tw-content);
}
.btn-group .dropdown-toggle.arrow-none::after, .dropdown .dropdown-toggle.arrow-none::after{
  content: var(--tw-content);
  display: none;
}
.btn-group .dropdown-menu, .dropdown .dropdown-menu{
  position: absolute;
  left: 0px;
  top: 100%;
  z-index: 10;
  min-width: 12rem;
  transform-origin: top;
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  border-radius: 0.25rem;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 0.5rem;
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
  opacity: 0;
  --tw-shadow: 0 4px 24px 0 rgba(62,57,107,.18);
  --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.btn-group .dropdown-menu:is([data-pc-theme="dark"] *), .dropdown .dropdown-menu:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.btn-group .dropdown-menu.dropdown-menu-end, .dropdown .dropdown-menu.dropdown-menu-end{
  right: 0px;
  left: auto;
}
.btn-group .dropdown-menu .dropdown-item, .dropdown .dropdown-menu .dropdown-item{
  display: block;
  display: flex;
  width: 100%;
  align-items: center;
  gap: 0.75rem;
  border-radius: 0.25rem;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 0.625rem;
  padding-right: 0.625rem;
  padding-left: 15px;
  padding-right: 15px;
}
.btn-group .dropdown-menu .dropdown-item:hover, .dropdown .dropdown-menu .dropdown-item:hover{
  background-color: rgb(178 188 197 / 0.2);
}
.btn-group .dropdown-menu .dropdown-item i, .dropdown .dropdown-menu .dropdown-item i{
  font-size: 1.125rem;
  line-height: 1.75rem;
  line-height: 1;
}
.btn-group .dropdown-menu .dropdown-item svg, .dropdown .dropdown-menu .dropdown-item svg{
  height: 18px;
  width: 18px;
}
.btn-group.drp-show .dropdown-menu, .dropdown.drp-show .dropdown-menu{
  display: block;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
  opacity: 1;
}
.form-control,.datatable-input{
  display: block;
  width: 100%;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
}
.form-control::-moz-placeholder, .datatable-input::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control::placeholder,.datatable-input::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control:focus,.datatable-input:focus{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  --tw-shadow: 0 0 0 1px #ccc;
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: rgb(4 169 245 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control:disabled,.datatable-input:disabled{
  pointer-events: none;
  background-color: rgb(155 168 180 / 0.1);
}
.form-control:is([data-pc-theme="dark"] *),.datatable-input:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
}
.form-control:focus:is([data-pc-theme="dark"] *),.datatable-input:focus:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  --tw-shadow-color: rgb(4 169 245 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.form-control[type="file"]::file-selector-button, .datatable-input[type="file"]::file-selector-button{
  margin-top: -0.625rem;
  margin-bottom: -0.625rem;
  margin-left: -1rem;
  margin-right: -1rem;
  margin-inline-end: 0.75rem;
  cursor: pointer;
  border-width: 0px;
  border-right-width: 1px;
  --tw-border-opacity: 1;
  border-right-color: rgb(190 200 208 / var(--tw-border-opacity));
  background-color: rgb(131 148 162 / 0.1);
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 1rem;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control[type="file"]:is([data-pc-theme="dark"] *)::file-selector-button, .datatable-input[type="file"]:is([data-pc-theme="dark"] *)::file-selector-button{
  --tw-border-opacity: 1;
  border-right-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.form-control.error, .datatable-input.error{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.form-control-plaintext{
  display: block;
  width: 100%;
  border-radius: 0.25rem;
  background-color: transparent;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 0.875rem;
}
.form-control-plaintext::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control-plaintext::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.form-control-plaintext:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-control-plaintext:disabled{
  pointer-events: none;
}
.form-control-plaintext:is([data-pc-theme="dark"] *){
  background-color: transparent;
}
.form-control-lg{
  border-radius: 0.5rem;
  padding-top: .775rem;
  padding-bottom: .775rem;
  padding-left: .85rem;
  padding-right: .85rem;
  font-size: 1.09375rem;
}
.form-control-sm{
  border-radius: 0.125rem;
  padding-top: .375rem;
  padding-bottom: .375rem;
  padding-left: .7rem;
  padding-right: .7rem;
  font-size: .765625rem;
}
.form-select, .datatable-selector{
  display: block;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231d2630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
  background-size: 16px 12px;
  background-repeat: no-repeat;
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
  font-size: 0.875rem;
}
.form-select:focus, .datatable-selector:focus{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  --tw-shadow: 0 0 0 1px #ccc;
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  --tw-shadow-color: #04A9F5;
  --tw-shadow: var(--tw-shadow-colored);
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.form-select:is([data-pc-theme="dark"] *), .datatable-selector:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23bfbfbf' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.form-select:where([dir="ltr"], [dir="ltr"] *), .datatable-selector:where([dir="ltr"], [dir="ltr"] *){
  background-position: right 1rem center;
  padding-left: 1rem;
  padding-right: 2rem;
}
.form-select:where([dir="rtl"], [dir="rtl"] *), .datatable-selector:where([dir="rtl"], [dir="rtl"] *){
  background-position: left 1rem center;
  padding-right: 1rem;
  padding-left: 2rem;
}
.form-select.error, .datatable-selector.error{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.form-select[multiple], .form-select[size]:not([size="1"]), [multiple].datatable-selector, [size].datatable-selector:not([size="1"]){
  background-image: none;
  padding-right: 0.75rem;
}
.form-select-lg{
  border-radius: 0.5rem;
  padding-top: .775rem;
  padding-bottom: .775rem;
  font-size: 1.09375rem;
}
.form-select-lg:where([dir="ltr"], [dir="ltr"] *){
  padding-left: .85rem;
  padding-right: 2rem;
}
.form-select-lg:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .85rem;
  padding-left: 2rem;
}
.form-select-sm{
  border-radius: 0.125rem;
  padding-top: .375rem;
  padding-bottom: .375rem;
  font-size: .765625rem;
}
.form-select-sm:where([dir="ltr"], [dir="ltr"] *){
  padding-left: .7rem;
  padding-right: 2rem;
}
.form-select-sm:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .7rem;
  padding-left: 2rem;
}
.form-control-color{
  height: calc(1.5em + 1.6rem + 2px);
  width: 3rem;
  padding: .8rem;
}
.form-control-color::-moz-color-swatch, .form-control-color::-webkit-color-swatch{
  border-radius: 0.25rem !important;
  border-width: 0px !important;
}
.input-group-text{
  display: flex;
  align-items: center;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  padding: 0.625rem 1rem;
  font-size: 0.875rem;
}
.input-group-text:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.input-group{
  position: relative;
  display: flex;
  width: 100%;
  flex-wrap: wrap;
  align-items: stretch;
}
.input-group > *{
  border-radius: 0px;
}
.input-group > *:where([dir="ltr"], [dir="ltr"] *){
  margin-left: -1px;
}
.input-group > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.input-group > *:last-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.input-group > *:where([dir="rtl"], [dir="rtl"] *){
  margin-right: -1px;
}
.input-group > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.input-group > *:last-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.input-group .form-control, .input-group .form-select{
  position: relative;
  width: 1%;
  min-width: 0px;
  flex: 1 1 auto;
}
.datepicker-cell.selected{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.datepicker-cell.selected:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.datepicker-cell.today.focused:not(.selected){
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.datepicker-cell.today.focused:not(.selected):hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.datepicker-view .week{
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.form-label{
  margin-bottom: 0.5rem;
  display: inline-block;
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.form-label:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
.col-form-label{
  padding-top: calc(.8rem + 1px);
  padding-bottom: calc(.8rem + 1px);
}
.col-form-label-sm{
  padding-top: calc(.375rem + 1px);
  padding-bottom: calc(.375rem + 1px);
}
.col-form-label-lg{
  padding-top: calc(.775rem + 1px);
  padding-bottom: calc(.775rem + 1px);
}
.form-check-input{
  height: 1.25em;
  width: 1.25em;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  vertical-align: text-bottom;
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.form-check-input:checked{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.form-check-input:disabled{
  pointer-events: none;
  opacity: 0.5;
}
.form-check-input:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.form-check-input[disabled] ~.form-check-label, .form-check-input:disabled ~.form-check-label{
  cursor: default;
  opacity: 0.5;
}
.form-check-input.error{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.form-check-input[type="checkbox"]{
  border-radius: 0.25rem;
}
.form-check-input[type="checkbox"]:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.form-check-input[type="radio"]{
  border-radius: 9999px;
}
.form-check-input[type="radio"]:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.form-check.form-switch{
  margin-bottom: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.form-check.form-switch .form-check-input{
  width: 2em;
  border-radius: 9999px;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
  background-position: left;
}
.form-check.form-switch .form-check-input:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
  background-position: right;
}
.form-range{
  height: 0.5rem;
  width: 100%;
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
}
.form-range:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.form-range::-webkit-slider-thumb{
  height: 1rem;
  width: 1rem;
  cursor: pointer;
  -webkit-appearance: none;
          appearance: none;
  border-radius: 9999px;
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.form-range:focus::-webkit-slider-thumb{
  opacity: 0.9;
}
.pc-toggle-noUiSlider{
  height: 50px;
}
.pc-toggle-noUiSlider.off .noUi-handle{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-target{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
}
.noUi-target:is([data-pc-theme="dark"] *){
  border-width: 0px;
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.noUi-handle:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.CodeMirror, .editor-toolbar{
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
.CodeMirror:is([data-pc-theme="dark"] *), .editor-toolbar:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.CodeMirror a, .editor-toolbar a{
  border-width: 0px;
  --tw-text-opacity: 1 !important;
  color: rgb(136 136 136 / var(--tw-text-opacity)) !important;
}
.CodeMirror a:is([data-pc-theme="dark"] *), .editor-toolbar a:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.CodeMirror a.active, .CodeMirror a:hover, .editor-toolbar a.active, .editor-toolbar a:hover{
  background-color: rgba(0,0,0,0.1);
  --tw-text-opacity: 1 !important;
  color: rgb(136 136 136 / var(--tw-text-opacity)) !important;
}
.CodeMirror a.active:is([data-pc-theme="dark"] *), .CodeMirror a:hover:is([data-pc-theme="dark"] *), .editor-toolbar a.active:is([data-pc-theme="dark"] *), .editor-toolbar a:hover:is([data-pc-theme="dark"] *){
  background-color: rgba(0,0,0,0.2);
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.CodeMirror i.separator, .editor-toolbar i.separator{
  border-right-color: transparent;
  --tw-border-opacity: 1;
  border-left-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.CodeMirror i.separator:is([data-pc-theme="dark"] *), .editor-toolbar i.separator:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-left-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.noUi-connect{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.switch-handle{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.switch-handle:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.typeahead{
  position: relative;
}
.typeahead>ul{
  position: absolute;
  top: 100%;
  left: 0px;
  float: left;
  margin: 2px 0 0;
  min-width: 170px;
  border-radius: 0.25rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 5px -;
  --tw-shadow: 0 6px 12px rgba(0,0,0,.17);
  --tw-shadow-colored: 0 6px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.typeahead>ul:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.typeahead>ul>li >a{
  display: block;
  white-space: nowrap;
  padding: 3px 20px;
  line-height: 1.5;
}
.typeahead>ul>li.active>a, .typeahead>ul>li.active>a:hover, .typeahead>ul>li>a:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
#cke5-inline-demo .ck-content{
  margin-bottom: 1rem;
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1rem;
}
#cke5-inline-demo .ck-content:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
@media (min-width: 640px){
  #cke5-inline-demo .ck-content{
    padding: 2.5rem;
  }
}
#cke5-inline-demo .ck-content .image-inline{
  float: right;
  margin-left: var(--ck-image-style-spacing);
  max-width: 50%;
}
#cke5-inline-demo header.ck-content{
  text-align: center;
}
#cke5-inline-demo header.ck-content h2 + h3{
  font-weight: 600;
}
#cke5-inline-demo .demo-row{
  display: flex;
  width: 100%;
  flex-direction: column;
}
@media (min-width: 640px){
  #cke5-inline-demo .demo-row{
    flex-direction: row;
  }
}
#cke5-inline-demo .demo-row .demo-row__half{
  width: 100%;
  padding-left: 0px;
  padding-right: 0px;
}
@media (min-width: 640px){
  #cke5-inline-demo .demo-row .demo-row__half{
    width: 50%;
  }
  #cke5-inline-demo .demo-row .demo-row__half:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 0.5rem;
  }
  #cke5-inline-demo .demo-row .demo-row__half:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 0.5rem;
  }
}
.dropzone{
  margin-bottom: 1.25rem;
  min-height: auto;
  cursor: pointer;
  border-radius: 0.25rem;
  border-width: 2px;
  border-style: dashed;
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  padding: 1.25rem;
}
.dropzone:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
}
.uppy-Dashboard--modal{
  z-index: 1030;
}
.uppy-Dashboard--modal .uppy-Dashboard-overlay{
  z-index: 1030;
}
.uppy-Dashboard--modal .uppy-Dashboard-inner{
  z-index: 1031;
}
.error-message{
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.datepicker-picker{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.datepicker-picker:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.datepicker-controls .btn{
  background-color: transparent;
}
.datepicker-cell.disabled{
  color: rgb(136 136 136 / 0.5);
}
.datepicker-cell.disabled:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.5);
}
.datepicker-cell.focused:not(.selected),.datepicker-cell:not(.disabled):hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.ql-container.ql-snow,.ql-toolbar.ql-snow{
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.ql-container.ql-snow:is([data-pc-theme="dark"] *),.ql-toolbar.ql-snow:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.ql-snow .ql-picker{
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
.ql-snow .ql-picker:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.ql-snow .ql-stroke{
  stroke: #888;
}
.ql-snow .ql-stroke:is([data-pc-theme="dark"] *){
  stroke: #bfbfbf;
}
.offcanvas{
  visibility: hidden;
  position: fixed;
  z-index: 1028;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
  --tw-shadow: 0 0 15px -3px rgb(0,0,0,0.1);
  --tw-shadow-colored: 0 0 15px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  transition-property: all;
  transition-duration: 500ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.offcanvas:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.offcanvas:not(.show){
  z-index: 1028;
}
.offcanvas .offcanvas-header{
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.25rem;
}
.offcanvas .offcanvas-body{
  padding: 1.25rem;
}
.offcanvas .offcanvas-body::-webkit-scrollbar{
  width: 0.375rem;
  opacity: 0;
}
.offcanvas .offcanvas-body::-webkit-scrollbar:hover{
  opacity: 1;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-track{
  background-color: transparent;
}
.offcanvas .offcanvas-body::-webkit-scrollbar-thumb{
  --tw-bg-opacity: 1;
  background-color: rgb(178 188 197 / var(--tw-bg-opacity));
}
.offcanvas .offcanvas-body::-webkit-scrollbar-thumb:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(155 168 180 / var(--tw-bg-opacity));
}
.offcanvas.offcanvas-start{
  top: 0px;
  bottom: 0px;
  left: -360px;
  width: 360px;
  max-width: 100%;
}
.offcanvas.offcanvas-start.show{
  left: 0px;
}
.offcanvas.offcanvas-top{
  left: 0px;
  right: 0px;
  top: -320px;
  height: 320px;
}
.offcanvas.offcanvas-top.show{
  top: 0px;
}
.offcanvas.offcanvas-end{
  top: 0px;
  bottom: 0px;
  right: -360px;
  width: 360px;
  max-width: 100%;
}
.offcanvas.offcanvas-end.show{
  right: 0px;
}
.offcanvas.offcanvas-bottom{
  left: 0px;
  right: 0px;
  bottom: -320px;
  height: 320px;
}
.offcanvas.offcanvas-bottom.show{
  bottom: 0px;
}
.offcanvas.show{
  visibility: visible;
}
.simplebar-scrollbar{
  position: absolute;
  width: 0.375rem;
}
.simplebar-scrollbar::before{
  position: absolute;
  left: 0px;
  right: 0px;
  border-radius: 0.375rem;
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
  content: var(--tw-content);
  opacity: 0;
}
.simplebar-scrollbar:where([dir="ltr"], [dir="ltr"] *){
  right: 1px;
}
.simplebar-scrollbar:where([dir="rtl"], [dir="rtl"] *){
  left: 1px;
}
.simplebar-scrollbar{
  min-height: 10px;
}
.simplebar-scrollbar:before{
  content: ' ';
  transition: opacity 0.2s linear;
}
.simplebar-scrollbar.simplebar-visible:before{
  opacity: 0.5;
  transition: opacity 0s linear;
}
.table-responsive{
  overflow-x: auto;
}
.table{
  margin-bottom: 1rem;
  width: 100%;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
  vertical-align: top;
}
.table:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.table > :not(caption) > * > *{
  white-space: nowrap;
  padding: .7rem .75rem;
}
.table thead{
  background-color: rgba(248,249,250,0.5);
  vertical-align: bottom;
}
.table thead:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(70 72 76 / var(--tw-bg-opacity));
}
.table thead th{
  border-top-width: 1px;
  border-bottom-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(241 241 241 / var(--tw-border-opacity));
  border-bottom-color: rgb(241 241 241 / var(--tw-border-opacity));
  padding: .9rem .75rem;
  vertical-align: middle;
  font-size: 13px;
  text-transform: uppercase;
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.table thead th:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.table thead th:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.table thead th:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.table:not(:last-child)>:last-child>*{
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.table:not(:last-child)>:last-child>*:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.table td, .table th{
  border-bottom-width: 0px;
  border-top-width: 1px;
  --tw-border-opacity: 1;
  border-top-color: rgb(241 241 241 / var(--tw-border-opacity));
  vertical-align: middle !important;
}
.table td:is([data-pc-theme="dark"] *), .table th:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.table.table-hover tbody tr:hover{
  background-color: rgba(0,0,0,.04) !important;
}
.table.table-striped tbody tr:nth-child(odd){
  background-color: rgb(131 148 162 / 0.1);
}
.table.table-dark{
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.table.table-dark tbody tr td{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.table.table-xl th, .table.table-xl td{
  padding: 1.25rem .8rem;
}
.table.table-lg th, .table.table-lg td{
  padding: .9rem .8rem;
}
.table.table-sm th, .table.table-sm td{
  padding: .6rem .8rem;
}
.table.table-xs th, .table.table-xs td{
  padding: .4rem .8rem;
}
.table.table-bordered th, .table.table-bordered td{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.table.table-bordered th:is([data-pc-theme="dark"] *), .table.table-bordered td:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.table.table-borderless th, .table.table-borderless td{
  border-width: 0px !important;
}
.datatable-table > thead > tr > th:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.datatable-table > thead > tr > th:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.datatable-top{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.datatable-top::after{
  content: var(--tw-content);
  display: none;
}
@media not all and (min-width: 640px){
  .datatable-top{
    flex-direction: column;
    align-items: flex-start;
  }
}
.datatable-sorter:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1rem;
}
.datatable-sorter:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1rem;
}
.datatable-sorter:where([dir="ltr"], [dir="ltr"] *):before, .datatable-sorter:where([dir="ltr"], [dir="ltr"] *):after{
  right: 0.25rem;
}
.datatable-sorter:where([dir="rtl"], [dir="rtl"] *):before, .datatable-sorter:where([dir="rtl"], [dir="rtl"] *):after{
  left: 0.25rem;
  right: auto;
}
.datatable-sorter:before{
  --tw-border-opacity: 1;
  border-top-color: rgb(136 136 136 / var(--tw-border-opacity));
}
.datatable-sorter:is([data-pc-theme="dark"] *):before{
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.datatable-sorter:after{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(136 136 136 / var(--tw-border-opacity));
}
.datatable-sorter:is([data-pc-theme="dark"] *):after{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.datatable-wrapper .datatable-container{
  overflow-x: auto;
  border-bottom-width: 0px !important;
}
.datatable-dropdown{
  margin-bottom: 0.25rem;
}
.datatable-dropdown label{
  display: flex;
  width: 230px;
  align-items: center;
  white-space: nowrap;
}
.datatable-dropdown label select:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
  margin-right: 0.5rem;
}
.datatable-dropdown label select:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
  margin-left: 0.5rem;
}
.datatable-pagination a:focus, .datatable-pagination a:hover, .datatable-pagination button:focus, .datatable-pagination button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination a:focus:is([data-pc-theme="dark"] *), .datatable-pagination a:hover:is([data-pc-theme="dark"] *), .datatable-pagination button:focus:is([data-pc-theme="dark"] *), .datatable-pagination button:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.datatable-pagination .datatable-active a,.datatable-pagination .datatable-active button{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination .datatable-active a:is([data-pc-theme="dark"] *),.datatable-pagination .datatable-active button:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.datatable-pagination .datatable-active a:focus, .datatable-pagination .datatable-active a:hover, .datatable-pagination .datatable-active button:focus, .datatable-pagination .datatable-active button:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.datatable-pagination .datatable-active a:focus:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active a:hover:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active button:focus:is([data-pc-theme="dark"] *), .datatable-pagination .datatable-active button:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
  color: rgba(255, 255, 255, 0.8);
}
.dt-container>div.row.justify-content-between{
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}
@media not all and (min-width: 768px){
  .dt-container>div.row.justify-content-between{
    text-align: center;
  }
  .dt-container>div.row.justify-content-between > *{
    margin-left: auto;
    margin-right: auto;
    margin-bottom: 0.5rem;
  }
}
@media (min-width: 768px){
  .dt-container>div.row.justify-content-between{
    flex-direction: row;
  }
}
.dt-container>div.row.mt-2{
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
div.dt-container div.dt-search input:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.25rem;
}
div.dt-container div.dt-search input:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0.25rem;
}
div.dt-container div.dt-length select{
  width: 5rem;
}
table.dataTable thead>tr> th:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2rem;
  text-align: left;
}
table.dataTable thead>tr> th:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem !important;
  padding-right: .75rem !important;
  text-align: right;
}
table.dataTable thead>tr> th .dt-column-order:where([dir="ltr"], [dir="ltr"] *){
  right: 0.75rem !important;
}
table.dataTable thead>tr> th .dt-column-order:where([dir="rtl"], [dir="rtl"] *){
  left: 0.75rem !important;
  right: auto !important;
}
.dt-search{
  margin-bottom: 0.5rem;
}
.dt-buttons{
  margin-bottom: 0.25rem;
}
.dt-buttons~.dt-search{
  margin-bottom: 1rem;
}
div.dt-container div.dt-paging ul.pagination, .dt-paging .pagination{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
  display: flex;
}
div.dt-container div.dt-paging ul.pagination > * > *, .dt-paging .pagination > * > *{
  display: inline-block;
}
div.dt-container div.dt-paging ul.pagination > *, .dt-paging .pagination > *{
  border-width: 1px;
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
}
div.dt-container div.dt-paging ul.pagination > * > *, .dt-paging .pagination > * > *{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
div.dt-container div.dt-paging ul.pagination > *:first-child, .dt-paging .pagination > *:first-child{
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
div.dt-container div.dt-paging ul.pagination > *:last-child, .dt-paging .pagination > *:last-child{
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
div.dt-container div.dt-paging ul.pagination > *:hover, .dt-paging .pagination > *:hover{
  background-color: rgb(131 148 162 / 0.1);
}
div.dt-container div.dt-paging ul.pagination:is([data-pc-theme="dark"] *) > *, .dt-paging .pagination:is([data-pc-theme="dark"] *) > *{
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
div.dt-container div.dt-paging ul.pagination .active>.page-link, div.dt-container div.dt-paging ul.pagination .page-link.active, .dt-paging .pagination .active>.page-link, .dt-paging .pagination .page-link.active{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
div.dt-scroll-body{
  border-bottom-width: 0px;
}
.dtfh-floatingparent.dtfh-floatingparent-head{
  top: 74px !important;
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-head:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-foot{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.dtfh-floatingparent.dtfh-floatingparent-foot:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
table.dataTable tbody tr >.dtfc-fixed-start, table.dataTable tbody tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.dataTable tbody tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *), table.dataTable tbody tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
table.dataTable thead tr >.dtfc-fixed-start,table.dataTable thead tr >.dtfc-fixed-end,table.dataTable tfoot tr >.dtfc-fixed-start,table.dataTable tfoot tr >.dtfc-fixed-end{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
table.dataTable thead tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.dataTable thead tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *),table.dataTable tfoot tr >.dtfc-fixed-start:is([data-pc-theme="dark"] *),table.dataTable tfoot tr >.dtfc-fixed-end:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.table-card .card-body, .table-body.card-body {
  padding-left: 0px;
  padding-right: 0px;
  padding-top: 0px;
}
.table-card .card-body .datatable-top, .table-card .card-body .datatable-bottom, .table-body.card-body .datatable-top, .table-body.card-body .datatable-bottom{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .datatable-top, .table-card .card-body .datatable-bottom, .table-body.card-body .datatable-top, .table-body.card-body .datatable-bottom{
    padding-left: 25px;
    padding-right: 25px;
  }
}
.table-card .card-body .table > thead > tr > th, .table-body.card-body .table > thead > tr > th{
  border-top-width: 0px;
}
.table-card .card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1.25rem;
}
.table-card .card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:first-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 25px;
  }
  .table-card .card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-card .card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr td:last-child:where([dir="ltr"], [dir="ltr"] *), .table-body.card-body .table tr th:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 25px;
  }
}
.table-card .card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1.25rem;
}
.table-card .card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1.25rem;
}
@media (min-width: 640px){
  .table-card .card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:first-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 25px;
  }
  .table-card .card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-card .card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr td:last-child:where([dir="rtl"], [dir="rtl"] *), .table-body.card-body .table tr th:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 25px;
  }
}
.pointer-events-none{
  pointer-events: none;
}
.visible{
  visibility: visible;
}
.invisible{
  visibility: hidden;
}
.collapse{
  visibility: collapse;
}
.static{
  position: static;
}
.fixed{
  position: fixed;
}
.absolute{
  position: absolute;
}
.relative{
  position: relative;
}
.inset-0{
  inset: 0px;
}
.inset-x-0{
  left: 0px;
  right: 0px;
}
.inset-y-0{
  top: 0px;
  bottom: 0px;
}
.\!left-auto{
  left: auto !important;
}
.\!right-auto{
  right: auto !important;
}
.\!top-header-height{
  top: 74px !important;
}
.-bottom-px{
  bottom: -1px;
}
.bottom-0{
  bottom: 0px;
}
.bottom-1{
  bottom: 0.25rem;
}
.bottom-\[-100px\]{
  bottom: -100px;
}
.bottom-\[-320px\]{
  bottom: -320px;
}
.bottom-\[150px\]{
  bottom: 150px;
}
.bottom-\[50px\]{
  bottom: 50px;
}
.left-0{
  left: 0px;
}
.left-2\/4{
  left: 50%;
}
.left-\[-100px\]{
  left: -100px;
}
.left-\[-150px\]{
  left: -150px;
}
.left-\[-360px\]{
  left: -360px;
}
.left-auto{
  left: auto;
}
.right-0{
  right: 0px;
}
.right-\[-100px\]{
  right: -100px;
}
.right-\[-150px\]{
  right: -150px;
}
.right-\[-360px\]{
  right: -360px;
}
.right-\[30px\]{
  right: 30px;
}
.right-auto{
  right: auto;
}
.top-0{
  top: 0px;
}
.top-\[-100px\]{
  top: -100px;
}
.top-\[-320px\]{
  top: -320px;
}
.top-\[13px\]{
  top: 13px;
}
.top-\[150px\]{
  top: 150px;
}
.top-full{
  top: 100%;
}
.top-header-height{
  top: 74px;
}
.z-10{
  z-index: 10;
}
.z-50{
  z-index: 50;
}
.z-\[1024\]{
  z-index: 1024;
}
.z-\[1025\]{
  z-index: 1025;
}
.z-\[1026\]{
  z-index: 1026;
}
.z-\[1027\]{
  z-index: 1027;
}
.z-\[1028\]{
  z-index: 1028;
}
.z-\[1030\]{
  z-index: 1030;
}
.z-\[1031\]{
  z-index: 1031;
}
.z-\[1034\]{
  z-index: 1034;
}
.z-\[1\]{
  z-index: 1;
}
.z-\[5\]{
  z-index: 5;
}
.z-\[995\]{
  z-index: 995;
}
.col-span-12{
  grid-column: span 12 / span 12;
}
.col-span-6{
  grid-column: span 6 / span 6;
}
.float-end{
  float: inline-end;
}
.float-right{
  float: right;
}
.float-left{
  float: left;
}
.m-0{
  margin: 0px;
}
.m-\[2px_0_0\]{
  margin: 2px 0 0;
}
.\!my-2{
  margin-top: 0.5rem !important;
  margin-bottom: 0.5rem !important;
}
.-mx-4{
  margin-left: -1rem;
  margin-right: -1rem;
}
.-my-2\.5{
  margin-top: -0.625rem;
  margin-bottom: -0.625rem;
}
.mx-1{
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-10{
  margin-left: 2.5rem;
  margin-right: 2.5rem;
}
.mx-2{
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.mx-auto{
  margin-left: auto;
  margin-right: auto;
}
.my-1{
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2{
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3{
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4{
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-5{
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.mb-0{
  margin-bottom: 0px;
}
.mb-1{
  margin-bottom: 0.25rem;
}
.mb-2{
  margin-bottom: 0.5rem;
}
.mb-2\.5{
  margin-bottom: 0.625rem;
}
.mb-3{
  margin-bottom: 0.75rem;
}
.mb-4{
  margin-bottom: 1rem;
}
.mb-5{
  margin-bottom: 1.25rem;
}
.mb-6{
  margin-bottom: 1.5rem;
}
.mb-8{
  margin-bottom: 2rem;
}
.me-0{
  margin-inline-end: 0px;
}
.me-1{
  margin-inline-end: 0.25rem;
}
.me-2{
  margin-inline-end: 0.5rem;
}
.me-3{
  margin-inline-end: 0.75rem;
}
.me-auto{
  margin-inline-end: auto;
}
.ml-1{
  margin-left: 0.25rem;
}
.ml-2{
  margin-left: 0.5rem;
}
.ml-2\.5{
  margin-left: 0.625rem;
}
.ml-\[var\(--ck-image-style-spacing\)\]{
  margin-left: var(--ck-image-style-spacing);
}
.mr-0{
  margin-right: 0px;
}
.mr-1{
  margin-right: 0.25rem;
}
.mr-1\.5{
  margin-right: 0.375rem;
}
.mr-2{
  margin-right: 0.5rem;
}
.mr-2\.5{
  margin-right: 0.625rem;
}
.mr-3{
  margin-right: 0.75rem;
}
.ms-3{
  margin-inline-start: 0.75rem;
}
.ms-auto{
  margin-inline-start: auto;
}
.mt-1{
  margin-top: 0.25rem;
}
.mt-2{
  margin-top: 0.5rem;
}
.mt-3{
  margin-top: 0.75rem;
}
.mt-4{
  margin-top: 1rem;
}
.mt-6{
  margin-top: 1.5rem;
}
.mt-header-height{
  margin-top: 74px;
}
.block{
  display: block;
}
.inline-block{
  display: inline-block;
}
.inline{
  display: inline;
}
.flex{
  display: flex;
}
.inline-flex{
  display: inline-flex;
}
.table{
  display: table;
}
.grid{
  display: grid;
}
.contents{
  display: contents;
}
.hidden{
  display: none;
}
.h-1\.5{
  height: 0.375rem;
}
.h-10{
  height: 2.5rem;
}
.h-11{
  height: 2.75rem;
}
.h-12{
  height: 3rem;
}
.h-2{
  height: 0.5rem;
}
.h-3\.5{
  height: 0.875rem;
}
.h-4{
  height: 1rem;
}
.h-5{
  height: 1.25rem;
}
.h-6{
  height: 1.5rem;
}
.h-\[1\.25em\]{
  height: 1.25em;
}
.h-\[18px\]{
  height: 18px;
}
.h-\[22px\]{
  height: 22px;
}
.h-\[300px\]{
  height: 300px;
}
.h-\[30px\]{
  height: 30px;
}
.h-\[320px\]{
  height: 320px;
}
.h-\[50px\]{
  height: 50px;
}
.h-\[5px\]{
  height: 5px;
}
.h-\[60px\]{
  height: 60px;
}
.h-\[calc\(1\.5em_\+_1\.6rem_\+_2px\)\]{
  height: calc(1.5em + 1.6rem + 2px);
}
.h-\[calc\(100vh_-_74px\)\]{
  height: calc(100vh - 74px);
}
.h-auto{
  height: auto;
}
.h-full{
  height: 100%;
}
.h-header-height{
  height: 74px;
}
.min-h-\[auto\]{
  min-height: auto;
}
.min-h-\[calc\(100vh_-_135px\)\]{
  min-height: calc(100vh - 135px);
}
.min-h-screen{
  min-height: 100vh;
}
.w-1\.5{
  width: 0.375rem;
}
.w-10{
  width: 2.5rem;
}
.w-11{
  width: 2.75rem;
}
.w-12{
  width: 3rem;
}
.w-20{
  width: 5rem;
}
.w-3\.5{
  width: 0.875rem;
}
.w-4{
  width: 1rem;
}
.w-5{
  width: 1.25rem;
}
.w-6{
  width: 1.5rem;
}
.w-\[1\%\]{
  width: 1%;
}
.w-\[1\.25em\]{
  width: 1.25em;
}
.w-\[130px\]{
  width: 130px;
}
.w-\[18px\]{
  width: 18px;
}
.w-\[22px\]{
  width: 22px;
}
.w-\[230px\]{
  width: 230px;
}
.w-\[2em\]{
  width: 2em;
}
.w-\[300px\]{
  width: 300px;
}
.w-\[30px\]{
  width: 30px;
}
.w-\[360px\]{
  width: 360px;
}
.w-\[50px\]{
  width: 50px;
}
.w-\[60px\]{
  width: 60px;
}
.w-auto{
  width: auto;
}
.w-full{
  width: 100%;
}
.w-sidebar-width{
  width: 264px;
}
.min-w-0{
  min-width: 0px;
}
.min-w-48{
  min-width: 12rem;
}
.min-w-\[170px\]{
  min-width: 170px;
}
.max-w-10{
  max-width: 2.5rem;
}
.max-w-48{
  max-width: 12rem;
}
.max-w-\[350px\]{
  max-width: 350px;
}
.max-w-\[50\%\]{
  max-width: 50%;
}
.max-w-full{
  max-width: 100%;
}
.flex-1{
  flex: 1 1 0%;
}
.flex-auto{
  flex: 1 1 auto;
}
.flex-shrink-0{
  flex-shrink: 0;
}
.shrink-0{
  flex-shrink: 0;
}
.grow{
  flex-grow: 1;
}
.origin-top{
  transform-origin: top;
}
.-translate-x-2\/4{
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0{
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100{
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.08\]{
  --tw-scale-x: 1.08;
  --tw-scale-y: 1.08;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform{
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-none{
  transform: none;
}
@keyframes floating{
  0%{
    transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  100%{
    transform: rotate(360deg) translate(-10px) rotate(-360deg);
  }
}
.animate-\[floating_7s_infinite\]{
  animation: floating 7s infinite;
}
@keyframes floating{
  0%{
    transform: rotate(0deg) translate(-10px) rotate(0deg);
  }
  100%{
    transform: rotate(360deg) translate(-10px) rotate(-360deg);
  }
}
.animate-\[floating_9s_infinite\]{
  animation: floating 9s infinite;
}
@keyframes hitZak{
  0%{
    left: 0;
    transform: translateX(-1%);
  }
  100%{
    left: 100%;
    transform: translateX(-99%);
  }
}
.animate-\[hitZak_0\.6s_ease-in-out_infinite_alternate\]{
  animation: hitZak 0.6s ease-in-out infinite alternate;
}
@keyframes move-bg{
  to{
    background-position: 400% 0;
  }
}
.animate-\[move-bg_24s_infinite_linear\]{
  animation: move-bg 24s infinite linear;
}
.cursor-default{
  cursor: default;
}
.cursor-pointer{
  cursor: pointer;
}
.resize{
  resize: both;
}
.list-\[circle\]{
  list-style-type: circle;
}
.list-decimal{
  list-style-type: decimal;
}
.list-disc{
  list-style-type: disc;
}
.appearance-none{
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.grid-cols-1{
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-12{
  grid-template-columns: repeat(12, minmax(0, 1fr));
}
.grid-cols-\[repeat\(auto-fit\2c minmax\(8rem\2c 1fr\)\)\]{
  grid-template-columns: repeat(auto-fit,minmax(8rem,1fr));
}
.flex-row{
  flex-direction: row;
}
.flex-col{
  flex-direction: column;
}
.flex-wrap{
  flex-wrap: wrap;
}
.items-end{
  align-items: flex-end;
}
.items-center{
  align-items: center;
}
.items-stretch{
  align-items: stretch;
}
.justify-end{
  justify-content: flex-end;
}
.justify-center{
  justify-content: center;
}
.justify-between{
  justify-content: space-between;
}
.gap-1{
  gap: 0.25rem;
}
.gap-1\.5{
  gap: 0.375rem;
}
.gap-2{
  gap: 0.5rem;
}
.gap-3{
  gap: 0.75rem;
}
.gap-4{
  gap: 1rem;
}
.gap-6{
  gap: 1.5rem;
}
.gap-x-2{
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-3{
  -moz-column-gap: 0.75rem;
       column-gap: 0.75rem;
}
.gap-x-6{
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-y-3{
  row-gap: 0.75rem;
}
.gap-y-8{
  row-gap: 2rem;
}
.space-x-2 > :not([hidden]) ~ :not([hidden]){
  --tw-space-x-reverse: 0;
  margin-right: calc(0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.justify-self-end{
  justify-self: end;
}
.overflow-hidden{
  overflow: hidden;
}
.overflow-x-auto{
  overflow-x: auto;
}
.whitespace-normal{
  white-space: normal;
}
.whitespace-nowrap{
  white-space: nowrap;
}
.\!rounded{
  border-radius: 0.25rem !important;
}
.rounded{
  border-radius: 0.25rem;
}
.rounded-\[14px_14px_0_0\]{
  border-radius: 14px 14px 0 0;
}
.rounded-\[20px\]{
  border-radius: 20px;
}
.rounded-full{
  border-radius: 9999px;
}
.rounded-lg{
  border-radius: 0.5rem;
}
.rounded-md{
  border-radius: 0.375rem;
}
.rounded-sm{
  border-radius: 0.125rem;
}
.rounded-l{
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.\!border-0{
  border-width: 0px !important;
}
.border{
  border-width: 1px;
}
.border-0{
  border-width: 0px;
}
.border-2{
  border-width: 2px;
}
.border-4{
  border-width: 4px;
}
.border-y{
  border-top-width: 1px;
  border-bottom-width: 1px;
}
.\!border-b-0{
  border-bottom-width: 0px !important;
}
.border-b{
  border-bottom-width: 1px;
}
.border-b-0{
  border-bottom-width: 0px;
}
.border-l-0{
  border-left-width: 0px;
}
.border-r{
  border-right-width: 1px;
}
.border-r-0{
  border-right-width: 0px;
}
.border-t{
  border-top-width: 1px;
}
.border-t-0{
  border-top-width: 0px;
}
.border-dashed{
  border-style: dashed;
}
.border-danger{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.border-danger-500{
  --tw-border-opacity: 1;
  border-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.border-dark{
  --tw-border-opacity: 1;
  border-color: rgb(33 37 41 / var(--tw-border-opacity));
}
.border-info{
  --tw-border-opacity: 1;
  border-color: rgb(62 191 234 / var(--tw-border-opacity));
}
.border-primary{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
}
.border-primary-500{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
}
.border-secondary{
  --tw-border-opacity: 1;
  border-color: rgb(91 107 121 / var(--tw-border-opacity));
}
.border-success{
  --tw-border-opacity: 1;
  border-color: rgb(29 233 182 / var(--tw-border-opacity));
}
.border-theme-border{
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.border-theme-inputborder{
  --tw-border-opacity: 1;
  border-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.border-themedark-border{
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.border-themedark-inputborder{
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.border-transparent{
  border-color: transparent;
}
.border-warning{
  --tw-border-opacity: 1;
  border-color: rgb(244 194 43 / var(--tw-border-opacity));
}
.border-white{
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity));
}
.border-y-theme-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(241 241 241 / var(--tw-border-opacity));
  border-bottom-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.border-b-theme-bodycolor{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(136 136 136 / var(--tw-border-opacity));
}
.border-b-themedark-bodycolor{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.border-b-themedark-border{
  --tw-border-opacity: 1;
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.border-l-theme-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.border-l-themedark-border{
  --tw-border-opacity: 1;
  border-left-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.border-r-theme-inputborder{
  --tw-border-opacity: 1;
  border-right-color: rgb(190 200 208 / var(--tw-border-opacity));
}
.border-r-transparent{
  border-right-color: transparent;
}
.border-t-theme-bodycolor{
  --tw-border-opacity: 1;
  border-top-color: rgb(136 136 136 / var(--tw-border-opacity));
}
.border-t-theme-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.border-t-themedark-bodycolor{
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.border-t-themedark-border{
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.\!bg-transparent{
  background-color: transparent !important;
}
.bg-\[rgba\(0\2c 0\2c 0\2c \.15\)\]{
  background-color: rgba(0,0,0,.15);
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.04\)\]{
  background-color: rgba(0,0,0,0.04);
}
.bg-\[rgba\(0\2c 0\2c 0\2c 0\.1\)\]{
  background-color: rgba(0,0,0,0.1);
}
.bg-\[rgba\(248\2c 249\2c 250\2c 0\.5\)\]{
  background-color: rgba(248,249,250,0.5);
}
.bg-amber-500{
  --tw-bg-opacity: 1;
  background-color: rgb(245 158 11 / var(--tw-bg-opacity));
}
.bg-blue-500{
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity));
}
.bg-cyan-500{
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity));
}
.bg-danger-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 209 / var(--tw-bg-opacity));
}
.bg-danger-200{
  --tw-bg-opacity: 1;
  background-color: rgb(250 175 170 / var(--tw-bg-opacity));
}
.bg-danger-300{
  --tw-bg-opacity: 1;
  background-color: rgb(248 139 131 / var(--tw-bg-opacity));
}
.bg-danger-400{
  --tw-bg-opacity: 1;
  background-color: rgb(246 102 93 / var(--tw-bg-opacity));
}
.bg-danger-50{
  --tw-bg-opacity: 1;
  background-color: rgb(254 230 228 / var(--tw-bg-opacity));
}
.bg-danger-500{
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
}
.bg-danger-500\/0{
  background-color: rgb(244 66 54 / 0);
}
.bg-danger-500\/10{
  background-color: rgb(244 66 54 / 0.1);
}
.bg-danger-600{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
}
.bg-danger-700{
  --tw-bg-opacity: 1;
  background-color: rgb(176 20 10 / var(--tw-bg-opacity));
}
.bg-danger-800{
  --tw-bg-opacity: 1;
  background-color: rgb(123 14 7 / var(--tw-bg-opacity));
}
.bg-danger-900{
  --tw-bg-opacity: 1;
  background-color: rgb(70 8 4 / var(--tw-bg-opacity));
}
.bg-danger-950{
  --tw-bg-opacity: 1;
  background-color: rgb(43 5 2 / var(--tw-bg-opacity));
}
.bg-dark-100{
  --tw-bg-opacity: 1;
  background-color: rgb(106 119 131 / var(--tw-bg-opacity));
}
.bg-dark-200{
  --tw-bg-opacity: 1;
  background-color: rgb(88 98 109 / var(--tw-bg-opacity));
}
.bg-dark-300{
  --tw-bg-opacity: 1;
  background-color: rgb(69 78 86 / var(--tw-bg-opacity));
}
.bg-dark-400{
  --tw-bg-opacity: 1;
  background-color: rgb(51 57 64 / var(--tw-bg-opacity));
}
.bg-dark-50{
  --tw-bg-opacity: 1;
  background-color: rgb(115 129 142 / var(--tw-bg-opacity));
}
.bg-dark-500{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.bg-dark-500\/0{
  background-color: rgb(33 37 41 / 0);
}
.bg-dark-500\/10{
  background-color: rgb(33 37 41 / 0.1);
}
.bg-dark-600{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
}
.bg-dark-700{
  --tw-bg-opacity: 1;
  background-color: rgb(18 20 23 / var(--tw-bg-opacity));
}
.bg-dark-800{
  --tw-bg-opacity: 1;
  background-color: rgb(14 15 17 / var(--tw-bg-opacity));
}
.bg-dark-900{
  --tw-bg-opacity: 1;
  background-color: rgb(9 10 11 / var(--tw-bg-opacity));
}
.bg-dark-950{
  --tw-bg-opacity: 1;
  background-color: rgb(2 3 3 / var(--tw-bg-opacity));
}
.bg-emerald-500{
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity));
}
.bg-fuchsia-500{
  --tw-bg-opacity: 1;
  background-color: rgb(217 70 239 / var(--tw-bg-opacity));
}
.bg-gray-200{
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity));
}
.bg-gray-900\/20{
  background-color: rgb(17 24 39 / 0.2);
}
.bg-green-500{
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity));
}
.bg-indigo-500{
  --tw-bg-opacity: 1;
  background-color: rgb(99 102 241 / var(--tw-bg-opacity));
}
.bg-info-100{
  --tw-bg-opacity: 1;
  background-color: rgb(209 240 250 / var(--tw-bg-opacity));
}
.bg-info-200{
  --tw-bg-opacity: 1;
  background-color: rgb(172 228 246 / var(--tw-bg-opacity));
}
.bg-info-300{
  --tw-bg-opacity: 1;
  background-color: rgb(136 215 242 / var(--tw-bg-opacity));
}
.bg-info-400{
  --tw-bg-opacity: 1;
  background-color: rgb(99 203 238 / var(--tw-bg-opacity));
}
.bg-info-50{
  --tw-bg-opacity: 1;
  background-color: rgb(228 246 252 / var(--tw-bg-opacity));
}
.bg-info-500{
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
}
.bg-info-500\/0{
  background-color: rgb(62 191 234 / 0);
}
.bg-info-500\/10{
  background-color: rgb(62 191 234 / 0.1);
}
.bg-info-600{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
}
.bg-info-700{
  --tw-bg-opacity: 1;
  background-color: rgb(18 129 166 / var(--tw-bg-opacity));
}
.bg-info-800{
  --tw-bg-opacity: 1;
  background-color: rgb(13 90 115 / var(--tw-bg-opacity));
}
.bg-info-900{
  --tw-bg-opacity: 1;
  background-color: rgb(7 50 65 / var(--tw-bg-opacity));
}
.bg-info-950{
  --tw-bg-opacity: 1;
  background-color: rgb(4 31 39 / var(--tw-bg-opacity));
}
.bg-inherit{
  background-color: inherit;
}
.bg-lime-500{
  --tw-bg-opacity: 1;
  background-color: rgb(132 204 22 / var(--tw-bg-opacity));
}
.bg-orange-500{
  --tw-bg-opacity: 1;
  background-color: rgb(249 115 22 / var(--tw-bg-opacity));
}
.bg-pink-500{
  --tw-bg-opacity: 1;
  background-color: rgb(236 72 153 / var(--tw-bg-opacity));
}
.bg-primary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(159 224 253 / var(--tw-bg-opacity));
}
.bg-primary-100\/50{
  background-color: rgb(159 224 253 / 0.5);
}
.bg-primary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(119 210 253 / var(--tw-bg-opacity));
}
.bg-primary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(78 197 252 / var(--tw-bg-opacity));
}
.bg-primary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(38 184 251 / var(--tw-bg-opacity));
}
.bg-primary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(179 230 254 / var(--tw-bg-opacity));
}
.bg-primary-50\/20{
  background-color: rgb(179 230 254 / 0.2);
}
.bg-primary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.bg-primary-500\/0{
  background-color: rgb(4 169 245 / 0);
}
.bg-primary-500\/10{
  background-color: rgb(4 169 245 / 0.1);
}
.bg-primary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.bg-primary-700{
  --tw-bg-opacity: 1;
  background-color: rgb(2 93 135 / var(--tw-bg-opacity));
}
.bg-primary-800{
  --tw-bg-opacity: 1;
  background-color: rgb(1 55 79 / var(--tw-bg-opacity));
}
.bg-primary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(0 17 24 / var(--tw-bg-opacity));
}
.bg-primary-950{
  --tw-bg-opacity: 1;
  background-color: rgb(7 11 13 / var(--tw-bg-opacity));
}
.bg-purple-500{
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity));
}
.bg-red-500{
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity));
}
.bg-rose-500{
  --tw-bg-opacity: 1;
  background-color: rgb(244 63 94 / var(--tw-bg-opacity));
}
.bg-secondary-100{
  --tw-bg-opacity: 1;
  background-color: rgb(178 188 197 / var(--tw-bg-opacity));
}
.bg-secondary-100\/20{
  background-color: rgb(178 188 197 / 0.2);
}
.bg-secondary-200{
  --tw-bg-opacity: 1;
  background-color: rgb(155 168 180 / var(--tw-bg-opacity));
}
.bg-secondary-300{
  --tw-bg-opacity: 1;
  background-color: rgb(131 148 162 / var(--tw-bg-opacity));
}
.bg-secondary-300\/10{
  background-color: rgb(131 148 162 / 0.1);
}
.bg-secondary-400{
  --tw-bg-opacity: 1;
  background-color: rgb(109 128 144 / var(--tw-bg-opacity));
}
.bg-secondary-50{
  --tw-bg-opacity: 1;
  background-color: rgb(190 198 206 / var(--tw-bg-opacity));
}
.bg-secondary-50\/10{
  background-color: rgb(190 198 206 / 0.1);
}
.bg-secondary-50\/20{
  background-color: rgb(190 198 206 / 0.2);
}
.bg-secondary-500{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.bg-secondary-500\/0{
  background-color: rgb(91 107 121 / 0);
}
.bg-secondary-500\/10{
  background-color: rgb(91 107 121 / 0.1);
}
.bg-secondary-600{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.bg-secondary-700{
  --tw-bg-opacity: 1;
  background-color: rgb(43 50 57 / var(--tw-bg-opacity));
}
.bg-secondary-800{
  --tw-bg-opacity: 1;
  background-color: rgb(19 22 25 / var(--tw-bg-opacity));
}
.bg-secondary-900{
  --tw-bg-opacity: 1;
  background-color: rgb(11 13 15 / var(--tw-bg-opacity));
}
.bg-secondary-950{
  --tw-bg-opacity: 1;
  background-color: rgb(4 5 6 / var(--tw-bg-opacity));
}
.bg-sky-500{
  --tw-bg-opacity: 1;
  background-color: rgb(14 165 233 / var(--tw-bg-opacity));
}
.bg-success-100{
  --tw-bg-opacity: 1;
  background-color: rgb(178 247 230 / var(--tw-bg-opacity));
}
.bg-success-200{
  --tw-bg-opacity: 1;
  background-color: rgb(141 244 218 / var(--tw-bg-opacity));
}
.bg-success-300{
  --tw-bg-opacity: 1;
  background-color: rgb(103 240 206 / var(--tw-bg-opacity));
}
.bg-success-400{
  --tw-bg-opacity: 1;
  background-color: rgb(66 237 194 / var(--tw-bg-opacity));
}
.bg-success-50{
  --tw-bg-opacity: 1;
  background-color: rgb(196 249 236 / var(--tw-bg-opacity));
}
.bg-success-500{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.bg-success-500\/0{
  background-color: rgb(29 233 182 / 0);
}
.bg-success-500\/10{
  background-color: rgb(29 233 182 / 0.1);
}
.bg-success-600{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
}
.bg-success-700{
  --tw-bg-opacity: 1;
  background-color: rgb(13 137 106 / var(--tw-bg-opacity));
}
.bg-success-800{
  --tw-bg-opacity: 1;
  background-color: rgb(8 85 66 / var(--tw-bg-opacity));
}
.bg-success-900{
  --tw-bg-opacity: 1;
  background-color: rgb(3 34 27 / var(--tw-bg-opacity));
}
.bg-success-950{
  --tw-bg-opacity: 1;
  background-color: rgb(1 9 7 / var(--tw-bg-opacity));
}
.bg-teal-500{
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity));
}
.bg-theme-bodybg{
  --tw-bg-opacity: 1;
  background-color: rgb(244 247 250 / var(--tw-bg-opacity));
}
.bg-theme-bodycolor{
  --tw-bg-opacity: 1;
  background-color: rgb(136 136 136 / var(--tw-bg-opacity));
}
.bg-theme-cardbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-headerbg{
  background-color: rgba( 244,247,250, 0.7);
}
.bg-theme-inputbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-lightsidebarbg{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-theme-sidebarbg{
  --tw-bg-opacity: 1;
  background-color: rgb(63 77 103 / var(--tw-bg-opacity));
}
.bg-theme-sidebarbg\/40{
  background-color: rgb(63 77 103 / 0.4);
}
.bg-themedark-bodybg{
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.bg-themedark-bodycolor{
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
.bg-themedark-cardbg{
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.bg-themedark-inputbg{
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
}
.bg-transparent{
  background-color: transparent;
}
.bg-violet-500{
  --tw-bg-opacity: 1;
  background-color: rgb(139 92 246 / var(--tw-bg-opacity));
}
.bg-warning-100{
  --tw-bg-opacity: 1;
  background-color: rgb(252 239 198 / var(--tw-bg-opacity));
}
.bg-warning-200{
  --tw-bg-opacity: 1;
  background-color: rgb(250 227 159 / var(--tw-bg-opacity));
}
.bg-warning-300{
  --tw-bg-opacity: 1;
  background-color: rgb(248 216 121 / var(--tw-bg-opacity));
}
.bg-warning-400{
  --tw-bg-opacity: 1;
  background-color: rgb(246 205 82 / var(--tw-bg-opacity));
}
.bg-warning-50{
  --tw-bg-opacity: 1;
  background-color: rgb(253 244 218 / var(--tw-bg-opacity));
}
.bg-warning-500{
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
}
.bg-warning-500\/0{
  background-color: rgb(244 194 43 / 0);
}
.bg-warning-500\/10{
  background-color: rgb(244 194 43 / 0.1);
}
.bg-warning-600{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
}
.bg-warning-700{
  --tw-bg-opacity: 1;
  background-color: rgb(166 127 9 / var(--tw-bg-opacity));
}
.bg-warning-800{
  --tw-bg-opacity: 1;
  background-color: rgb(113 86 6 / var(--tw-bg-opacity));
}
.bg-warning-900{
  --tw-bg-opacity: 1;
  background-color: rgb(60 45 3 / var(--tw-bg-opacity));
}
.bg-warning-950{
  --tw-bg-opacity: 1;
  background-color: rgb(33 25 2 / var(--tw-bg-opacity));
}
.bg-white{
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity));
}
.bg-white\/10{
  background-color: rgb(255 255 255 / 0.1);
}
.bg-yellow-500{
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity));
}
.bg-\[linear-gradient\(0deg\2c rgba\(0\2c 0\2c 0\2c 0\.5019607843\)\2c transparent\)\]{
  background-image: linear-gradient(0deg,rgba(0,0,0,0.5019607843),transparent);
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-1\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-1.jpg");
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-2\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-2.jpg");
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-3\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-3.jpg");
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-4\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-4.jpg");
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-5\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-5.jpg");
}
.bg-\[url\(\"\.\.\/images\/layout\/navbar-img-6\.jpg\"\)\]{
  background-image: url("../images/layout/navbar-img-6.jpg");
}
.bg-checkbox-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.bg-gradient-to-r{
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-none{
  background-image: none;
}
.bg-radio-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.bg-select-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%231d2630' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.bg-switch-active-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.bg-switch-bg{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='rgba%280, 0, 0, 0.25%29'/%3e%3c/svg%3e");
}
.bg-theme-bg-1{
  background-image: linear-gradient(-135deg, #1de9b6 0%, #1dc4e9 100%);
}
.bg-theme-bg-2{
  background-image: linear-gradient(-135deg, #899fd4 0%, #a389d4 100%);
}
.from-\[rgb\(37\2c 161\2c 244\)\]{
  --tw-gradient-from: rgb(37,161,244) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(37 161 244 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-\[rgb\(249\2c 31\2c 169\)\]{
  --tw-gradient-to: rgb(249 31 169 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(249,31,169) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-\[rgb\(37\2c 161\2c 244\)\]{
  --tw-gradient-to: rgb(37,161,244) var(--tw-gradient-to-position);
}
.bg-\[length\:16px_12px\]{
  background-size: 16px 12px;
}
.bg-\[length\:400\%_100\%\]{
  background-size: 400% 100%;
}
.bg-contain{
  background-size: contain;
}
.bg-clip-text{
  -webkit-background-clip: text;
          background-clip: text;
}
.bg-center{
  background-position: center;
}
.bg-left{
  background-position: left;
}
.bg-left-top{
  background-position: left top;
}
.bg-no-repeat{
  background-repeat: no-repeat;
}
.\!fill-themedark-inputbg{
  fill: #393b3f !important;
}
.\!fill-white{
  fill: #fff !important;
}
.stroke-theme-bodycolor{
  stroke: #888;
}
.stroke-themedark-bodycolor{
  stroke: #bfbfbf;
}
.\!p-10{
  padding: 2.5rem !important;
}
.p-0{
  padding: 0px;
}
.p-1{
  padding: 0.25rem;
}
.p-2{
  padding: 0.5rem;
}
.p-4{
  padding: 1rem;
}
.p-5{
  padding: 1.25rem;
}
.p-6{
  padding: 1.5rem;
}
.p-\[\.4rem_\.8rem\]{
  padding: .4rem .8rem;
}
.p-\[\.6rem_\.8rem\]{
  padding: .6rem .8rem;
}
.p-\[\.7rem_\.75rem\]{
  padding: .7rem .75rem;
}
.p-\[\.8rem\]{
  padding: .8rem;
}
.p-\[\.9rem_\.75rem\]{
  padding: .9rem .75rem;
}
.p-\[\.9rem_\.8rem\]{
  padding: .9rem .8rem;
}
.p-\[0\.625rem_1rem\]{
  padding: 0.625rem 1rem;
}
.p-\[1\.25rem_\.8rem\]{
  padding: 1.25rem .8rem;
}
.p-\[25px\]{
  padding: 25px;
}
.p-\[3px_20px\]{
  padding: 3px 20px;
}
.p-\[5px_-\]{
  padding: 5px -;
}
.px-0{
  padding-left: 0px;
  padding-right: 0px;
}
.px-0\.5{
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}
.px-10{
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2{
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5{
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4{
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5{
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6{
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-\[\.7rem\]{
  padding-left: .7rem;
  padding-right: .7rem;
}
.px-\[\.85rem\]{
  padding-left: .85rem;
  padding-right: .85rem;
}
.px-\[0\.8em\]{
  padding-left: 0.8em;
  padding-right: 0.8em;
}
.px-\[15px\]{
  padding-left: 15px;
  padding-right: 15px;
}
.px-\[23px\]{
  padding-left: 23px;
  padding-right: 23px;
}
.px-\[25px\]{
  padding-left: 25px;
  padding-right: 25px;
}
.py-1{
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-2{
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5{
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-3{
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4{
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-\[\.375rem\]{
  padding-top: .375rem;
  padding-bottom: .375rem;
}
.py-\[\.775rem\]{
  padding-top: .775rem;
  padding-bottom: .775rem;
}
.py-\[0\.45em\]{
  padding-top: 0.45em;
  padding-bottom: 0.45em;
}
.py-\[15px\]{
  padding-top: 15px;
  padding-bottom: 15px;
}
.py-\[calc\(\.375rem_\+_1px\)\]{
  padding-top: calc(.375rem + 1px);
  padding-bottom: calc(.375rem + 1px);
}
.py-\[calc\(\.775rem_\+_1px\)\]{
  padding-top: calc(.775rem + 1px);
  padding-bottom: calc(.775rem + 1px);
}
.py-\[calc\(\.8rem_\+_1px\)\]{
  padding-top: calc(.8rem + 1px);
  padding-bottom: calc(.8rem + 1px);
}
.\!pb-0{
  padding-bottom: 0px !important;
}
.pb-0{
  padding-bottom: 0px;
}
.pb-2{
  padding-bottom: 0.5rem;
}
.pl-4{
  padding-left: 1rem;
}
.pr-2{
  padding-right: 0.5rem;
}
.pr-3{
  padding-right: 0.75rem;
}
.pr-\[30px\]{
  padding-right: 30px;
}
.pt-0{
  padding-top: 0px;
}
.pt-5{
  padding-top: 1.25rem;
}
.pt-6{
  padding-top: 1.5rem;
}
.pt-\[100px\]{
  padding-top: 100px;
}
.text-center{
  text-align: center;
}
.text-right{
  text-align: right;
}
.align-top{
  vertical-align: top;
}
.\!align-middle{
  vertical-align: middle !important;
}
.align-middle{
  vertical-align: middle;
}
.align-bottom{
  vertical-align: bottom;
}
.align-text-bottom{
  vertical-align: text-bottom;
}
.\!font-\[\"tabler-icons\"\]{
  font-family: "tabler-icons" !important;
}
.text-2xl{
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-\[\.75em\]{
  font-size: .75em;
}
.text-\[\.765625rem\]{
  font-size: .765625rem;
}
.text-\[1\.09375rem\]{
  font-size: 1.09375rem;
}
.text-\[10px\]{
  font-size: 10px;
}
.text-\[11px\]{
  font-size: 11px;
}
.text-\[12px\]{
  font-size: 12px;
}
.text-\[13px\]{
  font-size: 13px;
}
.text-\[14px\]{
  font-size: 14px;
}
.text-\[18px\]{
  font-size: 18px;
}
.text-\[1rem\]{
  font-size: 1rem;
}
.text-\[22px\]{
  font-size: 22px;
}
.text-\[30px\]{
  font-size: 30px;
}
.text-\[36px\]{
  font-size: 36px;
}
.text-\[80\%\]{
  font-size: 80%;
}
.text-base{
  font-size: 0.875rem;
}
.text-lg{
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm{
  font-size: 0.75rem;
}
.text-xl{
  font-size: 1.25rem;
  line-height: 1.75rem;
}
.text-xs{
  font-size: 0.75rem;
  line-height: 1rem;
}
.font-light{
  font-weight: 300;
}
.font-medium{
  font-weight: 500;
}
.font-normal{
  font-weight: 400;
}
.font-semibold{
  font-weight: 600;
}
.uppercase{
  text-transform: uppercase;
}
.lowercase{
  text-transform: lowercase;
}
.italic{
  font-style: italic;
}
.leading-\[1\.2\]{
  line-height: 1.2;
}
.leading-none{
  line-height: 1;
}
.leading-normal{
  line-height: 1.5;
}
.\!text-theme-bodycolor{
  --tw-text-opacity: 1 !important;
  color: rgb(136 136 136 / var(--tw-text-opacity)) !important;
}
.\!text-themedark-bodycolor{
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.text-amber-500{
  --tw-text-opacity: 1;
  color: rgb(245 158 11 / var(--tw-text-opacity));
}
.text-blue-500{
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity));
}
.text-cyan-500{
  --tw-text-opacity: 1;
  color: rgb(6 182 212 / var(--tw-text-opacity));
}
.text-danger{
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.text-danger-400{
  --tw-text-opacity: 1;
  color: rgb(246 102 93 / var(--tw-text-opacity));
}
.text-danger-500{
  --tw-text-opacity: 1;
  color: rgb(244 66 54 / var(--tw-text-opacity));
}
.text-dark{
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.text-dark-500{
  --tw-text-opacity: 1;
  color: rgb(33 37 41 / var(--tw-text-opacity));
}
.text-emerald-500{
  --tw-text-opacity: 1;
  color: rgb(16 185 129 / var(--tw-text-opacity));
}
.text-fuchsia-500{
  --tw-text-opacity: 1;
  color: rgb(217 70 239 / var(--tw-text-opacity));
}
.text-gray-900{
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity));
}
.text-green-500{
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity));
}
.text-indigo-500{
  --tw-text-opacity: 1;
  color: rgb(99 102 241 / var(--tw-text-opacity));
}
.text-info-500{
  --tw-text-opacity: 1;
  color: rgb(62 191 234 / var(--tw-text-opacity));
}
.text-lime-500{
  --tw-text-opacity: 1;
  color: rgb(132 204 22 / var(--tw-text-opacity));
}
.text-orange-500{
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity));
}
.text-pink-500{
  --tw-text-opacity: 1;
  color: rgb(236 72 153 / var(--tw-text-opacity));
}
.text-primary-500{
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.text-purple-500{
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity));
}
.text-red-500{
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity));
}
.text-rose-500{
  --tw-text-opacity: 1;
  color: rgb(244 63 94 / var(--tw-text-opacity));
}
.text-secondary-500{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-sky-500{
  --tw-text-opacity: 1;
  color: rgb(14 165 233 / var(--tw-text-opacity));
}
.text-slate-500{
  --tw-text-opacity: 1;
  color: rgb(100 116 139 / var(--tw-text-opacity));
}
.text-slate-900{
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity));
}
.text-success{
  --tw-text-opacity: 1;
  color: rgb(29 233 182 / var(--tw-text-opacity));
}
.text-success-500{
  --tw-text-opacity: 1;
  color: rgb(29 233 182 / var(--tw-text-opacity));
}
.text-teal-500{
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity));
}
.text-theme-bodycolor{
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
.text-theme-bodycolor\/50{
  color: rgb(136 136 136 / 0.5);
}
.text-theme-bodycolor\/70{
  color: rgb(136 136 136 / 0.7);
}
.text-theme-headercolor{
  --tw-text-opacity: 1;
  color: rgb(91 107 121 / var(--tw-text-opacity));
}
.text-theme-headings{
  --tw-text-opacity: 1;
  color: rgb(29 38 48 / var(--tw-text-opacity));
}
.text-theme-lightsidebarcaption{
  --tw-text-opacity: 1;
  color: rgb(63 77 103 / var(--tw-text-opacity));
}
.text-theme-lightsidebarcolor{
  --tw-text-opacity: 1;
  color: rgb(63 77 103 / var(--tw-text-opacity));
}
.text-theme-secondarytextcolor{
  color: rgba(33, 37, 41, 0.75);
}
.text-theme-sidebarcaption{
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
.text-theme-sidebarcolor{
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
.text-themedark-bodycolor{
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.text-themedark-bodycolor\/50{
  color: rgb(191 191 191 / 0.5);
}
.text-themedark-bodycolor\/70{
  color: rgb(191 191 191 / 0.7);
}
.text-themedark-headings{
  color: rgba(255, 255, 255, 0.8);
}
.text-themedark-secondarytextcolor{
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.text-themedark-sidebarcaption{
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
.text-themedark-sidebarcolor{
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
.text-transparent{
  color: transparent;
}
.text-violet-500{
  --tw-text-opacity: 1;
  color: rgb(139 92 246 / var(--tw-text-opacity));
}
.text-warning-500{
  --tw-text-opacity: 1;
  color: rgb(244 194 43 / var(--tw-text-opacity));
}
.text-white{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.text-white\/60{
  color: rgb(255 255 255 / 0.6);
}
.text-white\/80{
  color: rgb(255 255 255 / 0.8);
}
.text-white\/\[90\]{
  color: rgb(255 255 255 / 90);
}
.text-yellow-500{
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity));
}
.underline{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.no-underline{
  -webkit-text-decoration-line: none;
          text-decoration-line: none;
}
.opacity-0{
  opacity: 0;
}
.opacity-100{
  opacity: 1;
}
.opacity-50{
  opacity: 0.5;
}
.opacity-75{
  opacity: 0.75;
}
.opacity-90{
  opacity: 0.9;
}
.\!shadow-none{
  --tw-shadow: 0 0 #0000 !important;
  --tw-shadow-colored: 0 0 #0000 !important;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow) !important;
}
.shadow-2xl{
  --tw-shadow: 0 25px 50px -12px rgb(0 0 0 / 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_15px_-3px_rgb\(0\2c 0\2c 0\2c 0\.1\)\]{
  --tw-shadow: 0 0 15px -3px rgb(0,0,0,0.1);
  --tw-shadow-colored: 0 0 15px -3px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_24px_rgba\(27\2c 46\2c 94\2c \.05\)\]{
  --tw-shadow: 0 0 24px rgba(27,46,94,.05);
  --tw-shadow-colored: 0 0 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_10px_20px_0_rgba\(0\2c 0\2c 0\2c 0\.3\)\]{
  --tw-shadow: 0 10px 20px 0 rgba(0,0,0,0.3);
  --tw-shadow-colored: 0 10px 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_1px_20px_0_rgba\(69\2c 90\2c 100\2c 0\.08\)\]{
  --tw-shadow: 0 1px 20px 0 rgba(69,90,100,0.08);
  --tw-shadow-colored: 0 1px 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_4px_24px_0_rgba\(62\2c 57\2c 107\2c \.18\)\]{
  --tw-shadow: 0 4px 24px 0 rgba(62,57,107,.18);
  --tw-shadow-colored: 0 4px 24px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_6px_12px_rgba\(0\2c 0\2c 0\2c \.17\)\]{
  --tw-shadow: 0 6px 12px rgba(0,0,0,.17);
  --tw-shadow-colored: 0 6px 12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_-6px_10px_0px_rgba\(12\2c 21\2c 70\2c 0\.03\)\]{
  --tw-shadow: 0px -6px 10px 0px rgba(12,21,70,0.03);
  --tw-shadow-colored: 0px -6px 10px 0px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0px_8px_24px_rgba\(27\2c 46\2c 94\2c 0\.08\)\]{
  --tw-shadow: 0px 8px 24px rgba(27,46,94,0.08);
  --tw-shadow-colored: 0px 8px 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[1px_0_3px_0_rgba\(219\2c 224\2c 229\2c 1\)\]{
  --tw-shadow: 1px 0 3px 0 rgba(219,224,229,1);
  --tw-shadow-colored: 1px 0 3px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lightsidebarshadow{
  --tw-shadow: 7px 0 15px 0 rgba(69, 90, 100, 0.09);
  --tw-shadow-colored: 7px 0 15px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-none{
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sidebarshadow{
  --tw-shadow: 1px 0 20px 0 #3f4d67;
  --tw-shadow-colored: 1px 0 20px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.outline-none{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline{
  outline-style: solid;
}
.\!outline-themedark-border{
  outline-color: #393b3f !important;
}
.blur{
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-\[0px_-6px_10px_rgba\(12\2c 21\2c 70\2c 0\.05\)\]{
  --tw-drop-shadow: drop-shadow(0px -6px 10px rgba(12,21,70,0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter{
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur{
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[3px\]{
  --tw-backdrop-blur: blur(3px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-\[7px\]{
  --tw-backdrop-blur: blur(7px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm{
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition{
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all{
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-200{
  transition-duration: 200ms;
}
.duration-500{
  transition-duration: 500ms;
}
.ease-in{
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out{
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.content-\[\"\"\]{
  --tw-content: "";
  content: var(--tw-content);
}
.content-\[\"\\\\ea1c\"\]{
  --tw-content: "\\ea1c";
  content: var(--tw-content);
}
.content-\[\"\\\\ea61\"\]{
  --tw-content: "\\ea61";
  content: var(--tw-content);
}
.content-\[\"\\\\ea65\"\]{
  --tw-content: "\\ea65";
  content: var(--tw-content);
}
.content-\[\"\\\\ea69\"\]{
  --tw-content: "\\ea69";
  content: var(--tw-content);
}
.content-\[\"\\\\ea7d\"\]{
  --tw-content: "\\ea7d";
  content: var(--tw-content);
}
.content-\[\"\\\\eaf2\"\]{
  --tw-content: "\\eaf2";
  content: var(--tw-content);
}
.content-\[\"\\\\eb0b\"\]{
  --tw-content: "\\eb0b";
  content: var(--tw-content);
}
.content-\[\"\\\\eb5f\"\]{
  --tw-content: "\\eb5f";
  content: var(--tw-content);
}
@media (min-width: 640px){
  .dt-container>div.sm\:mt-2.row{
    margin-top: 0.5rem !important;
    margin-bottom: 0.5rem !important;
  }
}
.\*\:relative > *{
  position: relative;
}
.\*\:m-\[5px\] > *{
  margin: 5px;
}
.\*\:\*\:inline-block > * > *{
  display: inline-block;
}
.\*\:inline-flex > *{
  display: inline-flex;
}
.\*\:h-\[70px\] > *{
  height: 70px;
}
.\*\:min-h-header-height > *{
  min-height: 74px;
}
.\*\:w-\[70px\] > *{
  width: 70px;
}
.\*\:cursor-pointer > *{
  cursor: pointer;
}
.\*\:items-center > *{
  align-items: center;
}
.\*\:justify-center > *{
  justify-content: center;
}
.\*\:rounded-lg > *{
  border-radius: 0.5rem;
}
.\*\:rounded-none > *{
  border-radius: 0px;
}
.\*\:border > *{
  border-width: 1px;
}
.\*\:border-theme-border > *{
  --tw-border-opacity: 1;
  border-color: rgb(241 241 241 / var(--tw-border-opacity));
}
.\*\:\*\:px-3 > * > *{
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.\*\:\*\:py-1\.5 > * > *{
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.placeholder\:text-\[\#bec8d0\]::-moz-placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.placeholder\:text-\[\#bec8d0\]::placeholder{
  --tw-text-opacity: 1;
  color: rgb(190 200 208 / var(--tw-text-opacity));
}
.before\:absolute::before{
  content: var(--tw-content);
  position: absolute;
}
.before\:inset-y-0::before{
  content: var(--tw-content);
  top: 0px;
  bottom: 0px;
}
.before\:bottom-0::before{
  content: var(--tw-content);
  bottom: 0px;
}
.before\:left-0::before{
  content: var(--tw-content);
  left: 0px;
}
.before\:right-0::before{
  content: var(--tw-content);
  right: 0px;
}
.before\:w-\[3px\]::before{
  content: var(--tw-content);
  width: 3px;
}
.before\:rounded-md::before{
  content: var(--tw-content);
  border-radius: 0.375rem;
}
.before\:bg-primary-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.before\:bg-slate-500::before{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(100 116 139 / var(--tw-bg-opacity));
}
.before\:font-\[\'tabler-icons\'\]::before{
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.before\:opacity-0::before{
  content: var(--tw-content);
  opacity: 0;
}
.before\:content-\[\"\"\]::before{
  --tw-content: "";
  content: var(--tw-content);
}
.before\:content-\[\'\2014\'\]::before{
  --tw-content: '—';
  content: var(--tw-content);
}
.after\:absolute::after{
  content: var(--tw-content);
  position: absolute;
}
.after\:inset-0::after{
  content: var(--tw-content);
  inset: 0px;
}
.after\:top-5::after{
  content: var(--tw-content);
  top: 1.25rem;
}
.after\:z-\[1\]::after{
  content: var(--tw-content);
  z-index: 1;
}
.after\:ml-1::after{
  content: var(--tw-content);
  margin-left: 0.25rem;
}
.after\:hidden::after{
  content: var(--tw-content);
  display: none;
}
.after\:h-\[5px\]::after{
  content: var(--tw-content);
  height: 5px;
}
.after\:w-\[5px\]::after{
  content: var(--tw-content);
  width: 5px;
}
.after\:scale-0::after{
  content: var(--tw-content);
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:scale-\[1\.2\]::after{
  content: var(--tw-content);
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.after\:rounded-full::after{
  content: var(--tw-content);
  border-radius: 9999px;
}
.after\:bg-dark-500::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.after\:bg-primary-500::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.after\:bg-theme-activebg::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(243 245 247 / var(--tw-bg-opacity));
}
.after\:bg-theme-sidebarcolor::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(169 183 208 / var(--tw-bg-opacity));
}
.after\:bg-white\/10::after{
  content: var(--tw-content);
  background-color: rgb(255 255 255 / 0.1);
}
.after\:align-bottom::after{
  content: var(--tw-content);
  vertical-align: bottom;
}
.after\:font-\[\'tabler-icons\'\]::after{
  content: var(--tw-content);
  font-family: 'tabler-icons';
}
.after\:text-base::after{
  content: var(--tw-content);
  font-size: 0.875rem;
}
.after\:opacity-0::after{
  content: var(--tw-content);
  opacity: 0;
}
.after\:opacity-10::after{
  content: var(--tw-content);
  opacity: 0.1;
}
.after\:opacity-100::after{
  content: var(--tw-content);
  opacity: 1;
}
.after\:transition::after{
  content: var(--tw-content);
  transition-property: color, background-color, border-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-text-decoration-color, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.after\:\!content-\[\"\"\]::after{
  --tw-content: "" !important;
  content: var(--tw-content) !important;
}
.after\:content-\[\"\"\]::after{
  --tw-content: "";
  content: var(--tw-content);
}
.after\:content-\[\'\\\\ea5f\'\]::after{
  --tw-content: '\\ea5f';
  content: var(--tw-content);
}
.first\:pt-2\.5:first-child{
  padding-top: 0.625rem;
}
.first\:\*\:rounded-l-lg > *:first-child{
  border-top-left-radius: 0.5rem;
  border-bottom-left-radius: 0.5rem;
}
.last\:opacity-75:last-child{
  opacity: 0.75;
}
.last\:\*\:rounded-r-lg > *:last-child{
  border-top-right-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
}
.odd\:bg-secondary-300\/10:nth-child(odd){
  background-color: rgb(131 148 162 / 0.1);
}
.checked\:border-primary-500:checked{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
}
.checked\:bg-primary-500:checked{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.checked\:bg-checkbox-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10l3 3l6-6'/%3e%3c/svg%3e");
}
.checked\:bg-radio-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='2' fill='%23ffffff'/%3e%3c/svg%3e");
}
.checked\:bg-switch-active-bg:checked{
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='-4 -4 8 8'%3e%3ccircle r='3' fill='%23ffffff'/%3e%3c/svg%3e");
}
.checked\:bg-right:checked{
  background-position: right;
}
.hover\:\!bg-\[rgba\(0\2c 0\2c 0\2c \.04\)\]:hover{
  background-color: rgba(0,0,0,.04) !important;
}
.hover\:bg-danger-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(252 211 209 / var(--tw-bg-opacity));
}
.hover\:bg-danger-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
}
.hover\:bg-danger-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
}
.hover\:bg-dark-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(106 119 131 / var(--tw-bg-opacity));
}
.hover\:bg-dark-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.hover\:bg-dark-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
}
.hover\:bg-gray-900:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(17 24 39 / var(--tw-bg-opacity));
}
.hover\:bg-info-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(209 240 250 / var(--tw-bg-opacity));
}
.hover\:bg-info-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
}
.hover\:bg-info-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
}
.hover\:bg-primary-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(159 224 253 / var(--tw-bg-opacity));
}
.hover\:bg-primary-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.hover\:bg-primary-500\/\[\.05\]:hover{
  background-color: rgb(4 169 245 / .05);
}
.hover\:bg-primary-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.hover\:bg-secondary-100\/20:hover{
  background-color: rgb(178 188 197 / 0.2);
}
.hover\:bg-secondary-100\/50:hover{
  background-color: rgb(178 188 197 / 0.5);
}
.hover\:bg-secondary-200:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(155 168 180 / var(--tw-bg-opacity));
}
.hover\:bg-secondary-50\/20:hover{
  background-color: rgb(190 198 206 / 0.2);
}
.hover\:bg-secondary-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.hover\:bg-secondary-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.hover\:bg-success-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(178 247 230 / var(--tw-bg-opacity));
}
.hover\:bg-success-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.hover\:bg-success-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
}
.hover\:bg-warning-100:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(252 239 198 / var(--tw-bg-opacity));
}
.hover\:bg-warning-500:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
}
.hover\:bg-warning-600:hover{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
}
.hover\:text-danger-600:hover{
  --tw-text-opacity: 1;
  color: rgb(229 26 13 / var(--tw-text-opacity));
}
.hover\:text-primary-500:hover{
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.hover\:text-theme-bodycolor:hover{
  --tw-text-opacity: 1;
  color: rgb(136 136 136 / var(--tw-text-opacity));
}
.hover\:text-white:hover{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.hover\:underline:hover{
  -webkit-text-decoration-line: underline;
          text-decoration-line: underline;
}
.hover\:opacity-100:hover{
  opacity: 1;
}
.hover\:opacity-70:hover{
  opacity: 0.7;
}
.hover\:\*\:bg-secondary-300\/10 > *:hover{
  background-color: rgb(131 148 162 / 0.1);
}
.hover\:after\:scale-100:hover::after{
  content: var(--tw-content);
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:after\:scale-\[1\.2\]:hover::after{
  content: var(--tw-content);
  --tw-scale-x: 1.2;
  --tw-scale-y: 1.2;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.hover\:after\:rounded:hover::after{
  content: var(--tw-content);
  border-radius: 0.25rem;
}
.hover\:after\:bg-dark-500:hover::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.hover\:after\:bg-primary-500:hover::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.hover\:after\:opacity-100:hover::after{
  content: var(--tw-content);
  opacity: 1;
}
.focus\:border-primary-500:focus{
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
}
.focus\:bg-danger-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(250 175 170 / var(--tw-bg-opacity));
}
.focus\:bg-danger-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(229 26 13 / var(--tw-bg-opacity));
}
.focus\:bg-dark-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(88 98 109 / var(--tw-bg-opacity));
}
.focus\:bg-dark-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(25 28 31 / var(--tw-bg-opacity));
}
.focus\:bg-info-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(172 228 246 / var(--tw-bg-opacity));
}
.focus\:bg-info-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(24 168 216 / var(--tw-bg-opacity));
}
.focus\:bg-primary-100\/50:focus{
  background-color: rgb(159 224 253 / 0.5);
}
.focus\:bg-primary-50\/20:focus{
  background-color: rgb(179 230 254 / 0.2);
}
.focus\:bg-primary-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(3 131 190 / var(--tw-bg-opacity));
}
.focus\:bg-secondary-200\/50:focus{
  background-color: rgb(155 168 180 / 0.5);
}
.focus\:bg-secondary-50\/20:focus{
  background-color: rgb(190 198 206 / 0.2);
}
.focus\:bg-secondary-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(67 79 89 / var(--tw-bg-opacity));
}
.focus\:bg-success-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(141 244 218 / var(--tw-bg-opacity));
}
.focus\:bg-success-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(18 188 145 / var(--tw-bg-opacity));
}
.focus\:bg-warning-200:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(250 227 159 / var(--tw-bg-opacity));
}
.focus\:bg-warning-600:focus{
  --tw-bg-opacity: 1;
  background-color: rgb(220 168 11 / var(--tw-bg-opacity));
}
.focus\:text-danger-600:focus{
  --tw-text-opacity: 1;
  color: rgb(229 26 13 / var(--tw-text-opacity));
}
.focus\:text-white:focus{
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.focus\:opacity-70:focus{
  opacity: 0.7;
}
.focus\:shadow-\[0_0_0_1px_\#ccc\]:focus{
  --tw-shadow: 0 0 0 1px #ccc;
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.focus\:shadow-primary-500:focus{
  --tw-shadow-color: #04A9F5;
  --tw-shadow: var(--tw-shadow-colored);
}
.focus\:shadow-primary-500\/10:focus{
  --tw-shadow-color: rgb(4 169 245 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
.focus\:outline-none:focus{
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.focus\:ring-2:focus{
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.focus\:ring-white:focus{
  --tw-ring-opacity: 1;
  --tw-ring-color: rgb(255 255 255 / var(--tw-ring-opacity));
}
.focus\:ring-offset-2:focus{
  --tw-ring-offset-width: 2px;
}
.focus\:ring-offset-gray-800:focus{
  --tw-ring-offset-color: #1f2937;
}
.active\:bg-danger-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(176 20 10 / var(--tw-bg-opacity));
}
.active\:bg-dark-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(18 20 23 / var(--tw-bg-opacity));
}
.active\:bg-info-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(18 129 166 / var(--tw-bg-opacity));
}
.active\:bg-primary-50\/30:active{
  background-color: rgb(179 230 254 / 0.3);
}
.active\:bg-primary-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(2 93 135 / var(--tw-bg-opacity));
}
.active\:bg-secondary-50\/30:active{
  background-color: rgb(190 198 206 / 0.3);
}
.active\:bg-secondary-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(43 50 57 / var(--tw-bg-opacity));
}
.active\:bg-success-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(13 137 106 / var(--tw-bg-opacity));
}
.active\:bg-warning-700:active{
  --tw-bg-opacity: 1;
  background-color: rgb(166 127 9 / var(--tw-bg-opacity));
}
.active\:text-danger-600:active{
  --tw-text-opacity: 1;
  color: rgb(229 26 13 / var(--tw-text-opacity));
}
.active\:opacity-70:active{
  opacity: 0.7;
}
.disabled\:pointer-events-none:disabled{
  pointer-events: none;
}
.disabled\:bg-secondary-200\/10:disabled{
  background-color: rgb(155 168 180 / 0.1);
}
.disabled\:opacity-50:disabled{
  opacity: 0.5;
}
.group:hover .group-hover\:opacity-100{
  opacity: 1;
}
.dark\:hidden:is([data-pc-theme="dark"] *){
  display: none;
}
.dark\:border-0:is([data-pc-theme="dark"] *){
  border-width: 0px;
}
.dark\:border-r:is([data-pc-theme="dark"] *){
  border-right-width: 1px;
}
.dark\:border-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:border-themedark-inputborder:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.dark\:border-white\/50:is([data-pc-theme="dark"] *){
  border-color: rgb(255 255 255 / 0.5);
}
.dark\:border-y-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:border-b-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.dark\:border-b-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-bottom-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:border-l-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-left-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:border-r-themedark-inputborder:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-right-color: rgb(70 72 76 / var(--tw-border-opacity));
}
.dark\:border-t-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(191 191 191 / var(--tw-border-opacity));
}
.dark\:border-t-themedark-border:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-top-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:\!bg-themedark-inputbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1 !important;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity)) !important;
}
.dark\:bg-\[\#46484c\]:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(70 72 76 / var(--tw-bg-opacity));
}
.dark\:bg-\[rgba\(0\2c 0\2c 0\2c 0\.2\)\]:is([data-pc-theme="dark"] *){
  background-color: rgba(0,0,0,0.2);
}
.dark\:bg-danger-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(244 66 54 / 0.1);
}
.dark\:bg-dark-500:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.dark\:bg-dark-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.dark\:bg-info-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(62 191 234 / 0.1);
}
.dark\:bg-primary-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(4 169 245 / 0.1);
}
.dark\:bg-secondary-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.dark\:bg-success-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(29 233 182 / 0.1);
}
.dark\:bg-themedark-bodybg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 34 36 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(191 191 191 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-cardbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-headerbg:is([data-pc-theme="dark"] *){
  background-color: rgba( 33, 34, 36, 0.7);
}
.dark\:bg-themedark-inputbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(57 59 63 / var(--tw-bg-opacity));
}
.dark\:bg-themedark-sidebarbg:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(43 44 47 / var(--tw-bg-opacity));
}
.dark\:bg-transparent:is([data-pc-theme="dark"] *){
  background-color: transparent;
}
.dark\:bg-warning-500\/10:is([data-pc-theme="dark"] *){
  background-color: rgb(244 194 43 / 0.1);
}
.dark\:bg-select-bg-dark:is([data-pc-theme="dark"] *){
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23bfbfbf' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3e%3c/svg%3e");
}
.dark\:\!fill-themedark-inputbg:is([data-pc-theme="dark"] *){
  fill: #393b3f !important;
}
.dark\:\!fill-white:is([data-pc-theme="dark"] *){
  fill: #fff !important;
}
.dark\:stroke-themedark-bodycolor:is([data-pc-theme="dark"] *){
  stroke: #bfbfbf;
}
.dark\:\!text-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1 !important;
  color: rgb(191 191 191 / var(--tw-text-opacity)) !important;
}
.dark\:text-slate-200:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(226 232 240 / var(--tw-text-opacity));
}
.dark\:text-slate-400:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(148 163 184 / var(--tw-text-opacity));
}
.dark\:text-themedark-bodycolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:text-themedark-bodycolor\/50:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.5);
}
.dark\:text-themedark-bodycolor\/70:is([data-pc-theme="dark"] *){
  color: rgb(191 191 191 / 0.7);
}
.dark\:text-themedark-headercolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(111 116 127 / var(--tw-text-opacity));
}
.dark\:text-themedark-headings:is([data-pc-theme="dark"] *){
  color: rgba(255, 255, 255, 0.8);
}
.dark\:text-themedark-secondarytextcolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(116 136 146 / var(--tw-text-opacity));
}
.dark\:text-themedark-sidebarcaption:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(232 237 247 / var(--tw-text-opacity));
}
.dark\:text-themedark-sidebarcolor:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(169 183 208 / var(--tw-text-opacity));
}
.dark\:text-white:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity));
}
.dark\:text-white\/50:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.5);
}
.dark\:text-white\/80:is([data-pc-theme="dark"] *){
  color: rgb(255 255 255 / 0.8);
}
.dark\:shadow-\[0_0_24px_rgba\(27\2c 46\2c 94\2c \.05\)\]:is([data-pc-theme="dark"] *){
  --tw-shadow: 0 0 24px rgba(27,46,94,.05);
  --tw-shadow-colored: 0 0 24px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-none:is([data-pc-theme="dark"] *){
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.dark\:shadow-\[none\]:is([data-pc-theme="dark"] *){
  --tw-shadow-color: none;
  --tw-shadow: var(--tw-shadow-colored);
}
.dark\:\!outline-themedark-border:is([data-pc-theme="dark"] *){
  outline-color: #393b3f !important;
}
.dark\:ring-1:is([data-pc-theme="dark"] *){
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.dark\:ring-inset:is([data-pc-theme="dark"] *){
  --tw-ring-inset: inset;
}
.dark\:ring-white\/10:is([data-pc-theme="dark"] *){
  --tw-ring-color: rgb(255 255 255 / 0.1);
}
.dark\:brightness-\[0\.1\]:is([data-pc-theme="dark"] *){
  --tw-brightness: brightness(0.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.\*\:dark\:border-themedark-border:is([data-pc-theme="dark"] *) > *{
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:\*\:border-themedark-border > *:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(57 59 63 / var(--tw-border-opacity));
}
.dark\:after\:bg-themedark-activebg:is([data-pc-theme="dark"] *)::after{
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(40 42 44 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-danger-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(244 66 54 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-dark-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(33 37 41 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-dark-500\/10:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(33 37 41 / 0.1);
}
.dark\:hover\:bg-info-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(62 191 234 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-primary-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(4 169 245 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-secondary-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(91 107 121 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-secondary-500\/10:hover:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.dark\:hover\:bg-success-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(29 233 182 / var(--tw-bg-opacity));
}
.dark\:hover\:bg-warning-500:hover:is([data-pc-theme="dark"] *){
  --tw-bg-opacity: 1;
  background-color: rgb(244 194 43 / var(--tw-bg-opacity));
}
.dark\:hover\:text-primary-500:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(4 169 245 / var(--tw-text-opacity));
}
.dark\:hover\:text-themedark-bodycolor:hover:is([data-pc-theme="dark"] *){
  --tw-text-opacity: 1;
  color: rgb(191 191 191 / var(--tw-text-opacity));
}
.dark\:focus\:border-primary-500:focus:is([data-pc-theme="dark"] *){
  --tw-border-opacity: 1;
  border-color: rgb(4 169 245 / var(--tw-border-opacity));
}
.dark\:focus\:bg-secondary-500\/10:focus:is([data-pc-theme="dark"] *){
  background-color: rgb(91 107 121 / 0.1);
}
.dark\:focus\:shadow-primary-500\/10:focus:is([data-pc-theme="dark"] *){
  --tw-shadow-color: rgb(4 169 245 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}
@media not all and (min-width: 1024px){
  .max-lg\:right-0{
    right: 0px;
  }
  .max-lg\:ml-0{
    margin-left: 0px;
  }
  .max-lg\:hidden{
    display: none;
  }
  .max-lg\:shadow-none{
    --tw-shadow: 0 0 #0000;
    --tw-shadow-colored: 0 0 #0000;
    box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
  }
}
@media not all and (min-width: 768px){
  .max-md\:text-center{
    text-align: center;
  }
  .max-md\:\*\:mx-auto > *{
    margin-left: auto;
    margin-right: auto;
  }
  .max-md\:\*\:mb-2 > *{
    margin-bottom: 0.5rem;
  }
}
@media not all and (min-width: 640px){
  .max-sm\:static{
    position: static;
  }
  .max-sm\:\!left-\[15px\]{
    left: 15px !important;
  }
  .max-sm\:\!right-\[15px\]{
    right: 15px !important;
  }
  .max-sm\:\!top-full{
    top: 100% !important;
  }
  .max-sm\:mr-3{
    margin-right: 0.75rem;
  }
  .max-sm\:min-w-\[calc\(100vw_-_30px\)\]{
    min-width: calc(100vw - 30px);
  }
  .max-sm\:\!transform-none{
    transform: none !important;
  }
  .max-sm\:flex-col{
    flex-direction: column;
  }
  .max-sm\:items-start{
    align-items: flex-start;
  }
  .max-sm\:p-\[15px\]{
    padding: 15px;
  }
  .max-sm\:px-2\.5{
    padding-left: 0.625rem;
    padding-right: 0.625rem;
  }
  .max-sm\:px-\[15px\]{
    padding-left: 15px;
    padding-right: 15px;
  }
}
@media (min-width: 640px){
  .sm\:relative{
    position: relative;
  }
  .sm\:col-span-3{
    grid-column: span 3 / span 3;
  }
  .sm\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .sm\:col-span-9{
    grid-column: span 9 / span 9;
  }
  .sm\:col-start-4{
    grid-column-start: 4;
  }
  .sm\:my-12{
    margin-top: 3rem;
    margin-bottom: 3rem;
  }
  .sm\:ml-2{
    margin-left: 0.5rem;
  }
  .sm\:mt-10{
    margin-top: 2.5rem;
  }
  .sm\:mt-2{
    margin-top: 0.5rem;
  }
  .sm\:block{
    display: block;
  }
  .sm\:inline-block{
    display: inline-block;
  }
  .sm\:hidden{
    display: none;
  }
  .sm\:w-2\/4{
    width: 50%;
  }
  .sm\:w-8\/12{
    width: 66.666667%;
  }
  .sm\:w-full{
    width: 100%;
  }
  .sm\:min-w-\[290px\]{
    min-width: 290px;
  }
  .sm\:min-w-\[320px\]{
    min-width: 320px;
  }
  .sm\:min-w-\[450px\]{
    min-width: 450px;
  }
  .sm\:grid-cols-1{
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  .sm\:grid-cols-11{
    grid-template-columns: repeat(11, minmax(0, 1fr));
  }
  .sm\:flex-row{
    flex-direction: row;
  }
  .sm\:items-stretch{
    align-items: stretch;
  }
  .sm\:justify-between{
    justify-content: space-between;
  }
  .sm\:space-y-1\.5 > :not([hidden]) ~ :not([hidden]){
    --tw-space-y-reverse: 0;
    margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
    margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
  }
  .sm\:p-10{
    padding: 2.5rem;
  }
  .sm\:px-\[25px\]{
    padding-left: 25px;
    padding-right: 25px;
  }
  .sm\:pt-\[180px\]{
    padding-top: 180px;
  }
  .sm\:text-\[0\.625rem\]{
    font-size: 0.625rem;
  }
  .sm\:text-\[16px\]{
    font-size: 16px;
  }
}
@media (min-width: 768px){
  .md\:col-span-6{
    grid-column: span 6 / span 6;
  }
  .md\:w-10\/12{
    width: 83.333333%;
  }
  .md\:max-w-\[540px\]{
    max-width: 540px;
  }
  .md\:flex-row{
    flex-direction: row;
  }
  .md\:text-\[36px\]{
    font-size: 36px;
  }
  .md\:text-xs{
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
@media (min-width: 1024px){
  .lg\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .lg\:inline-flex{
    display: inline-flex;
  }
  .lg\:hidden{
    display: none;
  }
  .lg\:w-0{
    width: 0px;
  }
  .lg\:max-w-\[720px\]{
    max-width: 720px;
  }
  .lg\:max-w-\[960px\]{
    max-width: 960px;
  }
  .lg\:text-\[0\.625rem\]{
    font-size: 0.625rem;
  }
  .lg\:text-\[48px\]{
    font-size: 48px;
  }
}
@media (min-width: 1280px){
  .xl\:col-span-4{
    grid-column: span 4 / span 4;
  }
  .xl\:col-span-8{
    grid-column: span 8 / span 8;
  }
}
@media (min-width: 1536px){
  .\32xl\:col-end-1{
    grid-column-end: 1;
  }
  .\32xl\:mt-0{
    margin-top: 0px;
  }
  .\32xl\:contents{
    display: contents;
  }
  .\32xl\:w-full{
    width: 100%;
  }
  .\32xl\:max-w-\[1140px\]{
    max-width: 1140px;
  }
  .\32xl\:pt-2\.5{
    padding-top: 0.625rem;
  }
  .\32xl\:text-xs{
    font-size: 0.75rem;
    line-height: 1rem;
  }
}
.ltr\:\!right-3:where([dir="ltr"], [dir="ltr"] *){
  right: 0.75rem !important;
}
.ltr\:right-0:where([dir="ltr"], [dir="ltr"] *){
  right: 0px;
}
.ltr\:right-1:where([dir="ltr"], [dir="ltr"] *){
  right: 0.25rem;
}
.ltr\:right-px:where([dir="ltr"], [dir="ltr"] *){
  right: 1px;
}
.ltr\:float-right:where([dir="ltr"], [dir="ltr"] *){
  float: right;
}
.ltr\:\!ml-0:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px !important;
}
.ltr\:ml-0:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
}
.ltr\:mr-1:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.25rem;
}
.ltr\:mr-2:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 0.5rem;
}
.ltr\:mr-4:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1rem;
}
.ltr\:mr-\[1\.35rem\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 1.35rem;
}
.ltr\:mr-\[15px\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 15px;
}
.ltr\:mr-\[5px\]:where([dir="ltr"], [dir="ltr"] *){
  margin-right: 5px;
}
.ltr\:border-r-0:where([dir="ltr"], [dir="ltr"] *){
  border-right-width: 0px;
}
.ltr\:bg-\[right_1rem_center\]:where([dir="ltr"], [dir="ltr"] *){
  background-position: right 1rem center;
}
.ltr\:pl-20:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 5rem;
}
.ltr\:pl-4:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1rem;
}
.ltr\:pl-\[\.7rem\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: .7rem;
}
.ltr\:pl-\[\.85rem\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: .85rem;
}
.ltr\:pl-\[60px\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 60px;
}
.ltr\:pl-\[95px\]:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 95px;
}
.ltr\:pr-4:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1rem;
}
.ltr\:pr-8:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 2rem;
}
.ltr\:text-left:where([dir="ltr"], [dir="ltr"] *){
  text-align: left;
}
.ltr\:text-right:where([dir="ltr"], [dir="ltr"] *){
  text-align: right;
}
.ltr\:\*\:-ml-px > *:where([dir="ltr"], [dir="ltr"] *){
  margin-left: -1px;
}
.ltr\:before\:right-\[-20px\]:where([dir="ltr"], [dir="ltr"] *)::before{
  content: var(--tw-content);
  right: -20px;
}
.ltr\:before\:content-\[\'\\\\ea61\'\]:where([dir="ltr"], [dir="ltr"] *)::before{
  --tw-content: '\\ea61';
  content: var(--tw-content);
}
.ltr\:after\:left-7:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 1.75rem;
}
.ltr\:after\:left-\[62px\]:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 62px;
}
.ltr\:after\:left-\[79px\]:where([dir="ltr"], [dir="ltr"] *)::after{
  content: var(--tw-content);
  left: 79px;
}
.ltr\:first\:pl-5:first-child:where([dir="ltr"], [dir="ltr"] *){
  padding-left: 1.25rem;
}
.ltr\:first\:\*\:ml-0 > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  margin-left: 0px;
}
.ltr\:first\:\*\:rounded-l > *:first-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
.ltr\:last\:pr-5:last-child:where([dir="ltr"], [dir="ltr"] *){
  padding-right: 1.25rem;
}
.ltr\:last\:\*\:rounded-r > *:last-child:where([dir="ltr"], [dir="ltr"] *){
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
@media not all and (min-width: 1024px){
  .ltr\:max-lg\:-left-sidebar-width:where([dir="ltr"], [dir="ltr"] *){
    left: -264px;
  }
  .ltr\:max-lg\:left-0:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
}
@media (min-width: 640px){
  .ltr\:first\:sm\:pl-\[25px\]:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 25px;
  }
  .ltr\:first\:sm\:pr-2:first-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 0.5rem;
  }
  .ltr\:last\:sm\:pl-2:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-left: 0.5rem;
  }
  .ltr\:last\:sm\:pr-\[25px\]:last-child:where([dir="ltr"], [dir="ltr"] *){
    padding-right: 25px;
  }
}
@media (min-width: 1024px){
  .ltr\:lg\:left-0:where([dir="ltr"], [dir="ltr"] *){
    left: 0px;
  }
  .ltr\:lg\:left-sidebar-width:where([dir="ltr"], [dir="ltr"] *){
    left: 264px;
  }
  .ltr\:lg\:ml-0:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 0px;
  }
  .ltr\:lg\:ml-sidebar-width:where([dir="ltr"], [dir="ltr"] *){
    margin-left: 264px;
  }
}
.rtl\:\!left-3:where([dir="rtl"], [dir="rtl"] *){
  left: 0.75rem !important;
}
.rtl\:\!left-auto:where([dir="rtl"], [dir="rtl"] *){
  left: auto !important;
}
.rtl\:\!right-0:where([dir="rtl"], [dir="rtl"] *){
  right: 0px !important;
}
.rtl\:\!right-auto:where([dir="rtl"], [dir="rtl"] *){
  right: auto !important;
}
.rtl\:left-0:where([dir="rtl"], [dir="rtl"] *){
  left: 0px;
}
.rtl\:left-1:where([dir="rtl"], [dir="rtl"] *){
  left: 0.25rem;
}
.rtl\:left-px:where([dir="rtl"], [dir="rtl"] *){
  left: 1px;
}
.rtl\:right-auto:where([dir="rtl"], [dir="rtl"] *){
  right: auto;
}
.rtl\:float-left:where([dir="rtl"], [dir="rtl"] *){
  float: left;
}
.rtl\:\!mr-0:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px !important;
}
.rtl\:ml-2:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 0.5rem;
}
.rtl\:ml-4:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1rem;
}
.rtl\:ml-\[1\.35rem\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 1.35rem;
}
.rtl\:ml-\[15px\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 15px;
}
.rtl\:ml-\[5px\]:where([dir="rtl"], [dir="rtl"] *){
  margin-left: 5px;
}
.rtl\:mr-0:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
}
.rtl\:mr-1:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0.25rem;
}
.rtl\:border-l-0:where([dir="rtl"], [dir="rtl"] *){
  border-left-width: 0px;
}
.rtl\:bg-\[left_1rem_center\]:where([dir="rtl"], [dir="rtl"] *){
  background-position: left 1rem center;
}
.rtl\:\!pl-8:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem !important;
}
.rtl\:\!pr-\[\.75rem\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .75rem !important;
}
.rtl\:pl-4:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1rem;
}
.rtl\:pl-8:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 2rem;
}
.rtl\:pr-20:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 5rem;
}
.rtl\:pr-4:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1rem;
}
.rtl\:pr-\[\.7rem\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .7rem;
}
.rtl\:pr-\[\.85rem\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: .85rem;
}
.rtl\:pr-\[60px\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 60px;
}
.rtl\:pr-\[95px\]:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 95px;
}
.rtl\:text-left:where([dir="rtl"], [dir="rtl"] *){
  text-align: left;
}
.rtl\:text-right:where([dir="rtl"], [dir="rtl"] *){
  text-align: right;
}
.rtl\:\*\:-mr-px > *:where([dir="rtl"], [dir="rtl"] *){
  margin-right: -1px;
}
.rtl\:before\:left-\[-20px\]:where([dir="rtl"], [dir="rtl"] *)::before{
  content: var(--tw-content);
  left: -20px;
}
.rtl\:before\:content-\[\'\\\\ea60\'\]:where([dir="rtl"], [dir="rtl"] *)::before{
  --tw-content: '\\ea60';
  content: var(--tw-content);
}
.rtl\:after\:right-7:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 1.75rem;
}
.rtl\:after\:right-\[62px\]:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 62px;
}
.rtl\:after\:right-\[79px\]:where([dir="rtl"], [dir="rtl"] *)::after{
  content: var(--tw-content);
  right: 79px;
}
.rtl\:first\:pr-5:first-child:where([dir="rtl"], [dir="rtl"] *){
  padding-right: 1.25rem;
}
.rtl\:first\:\*\:mr-0 > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  margin-right: 0px;
}
.rtl\:first\:\*\:rounded-r > *:first-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-right-radius: 0.25rem;
  border-bottom-right-radius: 0.25rem;
}
.rtl\:last\:pl-5:last-child:where([dir="rtl"], [dir="rtl"] *){
  padding-left: 1.25rem;
}
.rtl\:last\:\*\:rounded-l > *:last-child:where([dir="rtl"], [dir="rtl"] *){
  border-top-left-radius: 0.25rem;
  border-bottom-left-radius: 0.25rem;
}
@media not all and (min-width: 1024px){
  .rtl\:max-lg\:-right-sidebar-width:where([dir="rtl"], [dir="rtl"] *){
    right: -264px;
  }
  .rtl\:max-lg\:right-0:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
}
@media (min-width: 640px){
  .rtl\:first\:sm\:pl-2:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 0.5rem;
  }
  .rtl\:first\:sm\:pr-\[25px\]:first-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 25px;
  }
  .rtl\:last\:sm\:pl-\[25px\]:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-left: 25px;
  }
  .rtl\:last\:sm\:pr-2:last-child:where([dir="rtl"], [dir="rtl"] *){
    padding-right: 0.5rem;
  }
}
@media (min-width: 1024px){
  .rtl\:lg\:right-0:where([dir="rtl"], [dir="rtl"] *){
    right: 0px;
  }
  .rtl\:lg\:right-sidebar-width:where([dir="rtl"], [dir="rtl"] *){
    right: 264px;
  }
  .rtl\:lg\:mr-0:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 0px;
  }
  .rtl\:lg\:mr-sidebar-width:where([dir="rtl"], [dir="rtl"] *){
    margin-right: 264px;
  }
}
.\[\&\.complete\]\:border-t-\[9px\].complete{
  border-top-width: 9px;
}
.\[\&\.complete\]\:border-t-danger-500.complete{
  --tw-border-opacity: 1;
  border-top-color: rgb(244 66 54 / var(--tw-border-opacity));
}
.\[\&\.complete\]\:line-through.complete{
  -webkit-text-decoration-line: line-through;
          text-decoration-line: line-through;
}