{"name": "@types/luxon", "version": "3.7.1", "description": "TypeScript definitions for luxon", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/luxon", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "carsonf", "url": "https://github.com/carsonf"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"import": "./index.d.mts", "require": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/luxon"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "842ea2e5c187c7a297b1544e257f18d4b6e6cb345dd37dc5d4b157869f06a6a8", "typeScriptVersion": "5.1"}