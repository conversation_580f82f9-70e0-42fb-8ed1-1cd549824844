name: Prod deploy

# Controls when the action will run.
on:
  # Triggers the workflow on push or pull request events but only for the master branch
  push:
    branches:
      - master
  pull_request:
    types:
      - closed
    branches:
      - master

jobs:
  if_merged:
    if: github.event.pull_request.merged == true
    name: 🎉 Deploy
    runs-on: ubuntu-latest

    steps:
      - name: 🚚 Get latest code
        uses: actions/checkout@v4

      - name: Install Node.js 20
        uses: actions/setup-node@v4
        with:
          node-version: "20.x"

      - name: 🔨 Build Project
        run: |
          npm install
          npm run build

      - name: 📂 Deploy to Server
        uses: easingthemes/ssh-deploy@v4
        env:
          SSH_PRIVATE_KEY: ${{ secrets.SERVER_SSH_KEY }}
          # ARGS: "-rltgoDzvO --delete"
          SOURCE: 'dist/'
          REMOTE_HOST: **************
          REMOTE_USER: u778408432
          REMOTE_PORT: '65002'
          TARGET: domains/codedthemes.com/public_html/demos/admin-templates/datta-able/tailwind/free
          EXCLUDE: '/node_modules/'
